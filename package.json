{"name": "xinsiaodun_admin", "version": "0.1.0", "private": true, "scripts": {"serve": "NODE_OPTIONS='--openssl-legacy-provider' vue-cli-service serve", "build": "NODE_OPTIONS='--openssl-legacy-provider' vue-cli-service build"}, "dependencies": {"ant-design-vue": "^1.2.4", "axios": "^0.19.2", "core-js": "^3.6.5", "jquery": "^3.5.1", "less-loader": "^6.1.1", "mavon-editor": "^2.9.0", "vue": "^2.6.11", "vue-devtools": "^5.1.3", "vue-router": "^3.2.0", "vuex": "^3.4.0", "wangeditor": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-service": "~4.4.0", "vue-cli-plugin-ant-design": "^1.0.1", "vue-cli-plugin-element": "^1.0.1", "vue-template-compiler": "^2.6.11"}}