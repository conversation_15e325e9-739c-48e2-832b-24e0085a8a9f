<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<style>
/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 全局页面过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

/* 全局Ant Design组件样式优化 */
.ant-layout {
  background: #f5f5f5;
}

.ant-layout-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin: 16px 0;
  overflow: visible;
  transition: all 0.3s ease;
  min-height: auto;
  height: auto;
}

.ant-layout-content:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 面包屑导航样式优化 */
.ant-breadcrumb {
  margin: 16px 0 !important;
  padding: 8px 0;
  background: transparent;
}

.ant-breadcrumb .ant-breadcrumb-link {
  color: #666;
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 400;
  font-size: 14px;
}

.ant-breadcrumb .ant-breadcrumb-link:hover {
  color: #1890ff;
}

.ant-breadcrumb .ant-breadcrumb-link .anticon {
  font-size: 14px;
}

.ant-breadcrumb .ant-breadcrumb-separator {
  color: #d9d9d9;
  margin: 0 8px;
}

.ant-breadcrumb li:last-child .ant-breadcrumb-link {
  color: #262626;
  font-weight: 500;
}

/* 页面头部样式优化 */
.ant-page-header {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #f0f0f0 !important;
  transition: all 0.3s ease;
}

.ant-page-header:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.ant-page-header .ant-page-header-heading-title {
  color: #262626;
  font-weight: 600;
}

.ant-page-header .ant-page-header-heading-sub-title {
  color: #8c8c8c;
}

/* 按钮全局样式增强 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-btn.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
}

.ant-btn.ant-btn-primary:hover,
.ant-btn.ant-btn-primary:focus {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.ant-btn.ant-btn-danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border: none;
  color: white;
}

.ant-btn.ant-btn-danger:hover,
.ant-btn.ant-btn-danger:focus {
  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
  color: white;
}

/* 下拉菜单全局样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.ant-dropdown-menu .ant-dropdown-menu-item {
  transition: all 0.3s ease;
}

.ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background: rgba(24, 144, 255, 0.06);
  color: #1890ff;
}

/* 表单样式增强 */
.ant-form-item-label > label {
  color: #262626;
  font-weight: 500;
}

.ant-input,
.ant-select-selector,
.ant-picker {
  border-radius: 6px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-picker:hover {
  border-color: #d9d9d9;
}

.ant-input:focus,
.ant-input.ant-input-focused,
.ant-select-selector.ant-select-focused,
.ant-picker.ant-select-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 卡片样式增强 */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.ant-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.ant-card .ant-card-head .ant-card-head-title {
  color: #262626;
  font-weight: 600;
}

/* 消息提示样式优化 */
.ant-message .ant-message-notice-content {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 通知样式优化 */
.ant-notification .ant-notification-notice {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 加载动画优化 */
.ant-spin-dot .ant-spin-dot-item {
  background-color: #1890ff;
}

/* 页面加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-enter {
  animation: fadeInUp 0.6s ease-out;
}

/* 悬浮效果 */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}
</style>
