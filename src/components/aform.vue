
<template>
  
    <a-modal v-model="visible" title="编辑" @ok="handleOk" :confirm-loading="confirmLoading">
      <a-form-model-item label="Field B">
        <a-input  placeholder="input placeholder" />
      </a-form-model-item>
    </a-modal>
  
</template>

<script>
export default {
  name: 'atable',
  props: {
    formDisplay:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false
    };
  },
   watch:{
    formDisplay(val){
      this.visible = val;
    }
  },
  methods: {
    showModal() {
      this.visible = true;
    },
    handleOk(e) {
      console.log(e);
      

       this.confirmLoading = true;
        setTimeout(() => {
          this.visible = false;
          this.confirmLoading = false;
        }, 2000);
    },
  },
};
</script>