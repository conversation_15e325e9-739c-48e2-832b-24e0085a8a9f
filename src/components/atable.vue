<template>
<div>
 
  <a-table
    :columns="tabColumns"
    :data-source="pageData"
    bordered
    :pagination="paginationConfig"
    :row-key="record => record.key || record.id"
    @change="handleTableChange"
  >
     <span slot="create_time_label" slot-scope="text, record">
        {{record.create_time}}      
      </span>
     <span slot="operation" slot-scope="text, record" class="table-operation">
        <a-button icon="edit" type="primary" @click="onEdit(record.id)">
          编辑
        </a-button>
        &nbsp;&nbsp;
          <a-popconfirm
          v-if="tabSourceData.length"
          title="确定要删除?"
          @confirm="() => onDelete(record.id)"
        >
        <a-button icon="close" type="danger">
          删除
        </a-button>
        </a-popconfirm>
      </span>

     <span slot="operationDel" slot-scope="text, record" class="table-operation">
        &nbsp;&nbsp;
          <a-popconfirm
          v-if="tabSourceData.length"
          title="确定要删除?"
          @confirm="() => onDelete(record.id)"
        >
        <a-button icon="close" type="danger">
          删除
        </a-button>
        </a-popconfirm>
      </span>

        <!-- 图片弹窗-->
     <span slot="imgAlert" slot-scope="text, record">
         <a-button type="primary" @click="showModal(text)" v-if="text != ''">
           <a-icon type="eye" />
          查看
        </a-button>
        <div v-else>无</div>

         <a-modal
            title="图片查看"
            :visible="imgModelVisible"
            @ok="closeModal"
            @cancel="closeModal"
            :footer="null"
            width="80%"
          >

          <img :src="imgAlertImg" style="max-width:80%;"/>
          
        
          </a-modal>
      </span>


      <!-- 配置类型-->
     <span slot="configTypeLabel" slot-scope="text, record">
          <a-tag color="#f50" v-if="record.config_type == 1">
            文字
          </a-tag>
          <a-tag color="#2db7f5" v-if="record.config_type == 2">
            图片
          </a-tag>
          <a-tag color="#87d068" v-if="record.config_type == 3">
            富文本
          </a-tag>
      </span>


        <!-- 配置弹窗-->
       <span slot="configInfoAlert" slot-scope="text, record">

         <a-button type="primary" @click="configInfoAlertVisibleShow(record.config_value, record.config_type)" v-if="record.config_type != 1">
           <a-icon type="eye" />
          查看
        </a-button>

        <a-tooltip placement="bottom" v-else>
          <template slot="title">
            <span >{{record.config_value}}</span>
          </template>
          <a-button><a-icon type="bulb" />查看</a-button>
        </a-tooltip>

        

         <a-modal
            :title="record.config_key"
            :visible="configInfoAlertVisible"
            @ok="closeModal"
            @cancel="closeModal"
            :footer="null"
            width="80%"
          >
          

          <img :src="configInfoAlertVisibleText" v-if="configInfoAlertVisibleTextType == 2">
          <div v-else v-html="configInfoAlertVisibleText">
            
          </div>
        </a-modal>
      </span>


        <!-- 文字提示-->
       <span slot="toolTip" slot-scope="text, record">
           <a-tooltip placement="bottom">
            <template slot="title">
              <span >{{text}}</span>
            </template>
            <a-button><a-icon type="bulb" />查看</a-button>
          </a-tooltip>
      </span>


        <!-- 排序-->
       <span slot="dataSort" slot-scope="text, record">
         <a-input-number v-if="record.advantage_sort" v-model="record.advantage_sort" :min="1" :max="999" @blur="onDataSortChange(record.advantage_sort, record.id, 'advantage_sort')" style="width: 70px;text-align:center;" />
         <a-input-number v-if="record.index_sort" v-model="record.index_sort" :min="1" :max="999" @blur="onDataSortChange(record.index_sort, record.id, 'index_sort')" style="width: 70px;text-align:center;" />
      </span>


      <!-- html内容弹窗-->
       <span slot="htmlInfoAlert" slot-scope="text, record">
         <a-button type="primary" @click="htmlInfoAlertShow(text)">
           <a-icon type="eye" />
          查看
        </a-button>

         <a-modal
            title="内容查看"
            :visible="htmlContentAlertVisible"
            @ok="closeModal"
            @cancel="closeModal"
            :footer="null"
            width="80%"
          >
          <div  v-html="htmlInfoAlertInfo">
            
          </div>
        </a-modal>
      </span>


      <!-- tag-->
       <span slot="tag" slot-scope="text, record">
        <div v-if="record.detail_cover_position">
            <a-tag color="#f50" v-if="record.detail_cover_position == 1">
              左
            </a-tag>
            <a-tag color="#2db7f5" v-if="record.detail_cover_position == 2">
              右
            </a-tag>
        </div>
      </span>


      <!-- 开关-->
       <span slot="openClose" slot-scope="text, record">
        <div v-if="record.index_status">
          <a-switch checked-children="开启" un-checked-children="隐藏" :default-checked="record.index_status == 1?true:false" @change="onDataSwichChange(text, record.id, 'index_status')"/>
        </div>
      </span>



        <!-- 批量上传图片上传-->
     <span slot="infoImgUpload" slot-scope="text, record">
         <a-button type="primary" @click="infoImgUploadModelShow(record)" shape="round">
           <a-icon type="cloud-upload" />
          上传
        </a-button>
          /
        <a-button type="primary" @click="imgListOperateVisibleShow(record)" shape="round">
          <a-icon type="setting" />
          排序
        </a-button>
         <a-modal
            :title="infoImgUploadTitle+' 图片上传'"
            :visible="infoImgUploadVisible"
            @ok="onInfoImgUploadSave"
            @cancel="closeModal"
            width="50%"
            okText="保存"
            cancelText="取消"
            :confirm-loading="infoImgUploadSaveLoading"
            :maskClosable="true"
          >
          <auploadList :infoImgUploadVisible="infoImgUploadVisible" :infoImgUploadSaveStatus="infoImgUploadSaveStatus" func="getUploadInfoImgUpload" @getUploadInfoImgUpload="getUploadInfoImgUpload($event)"/>
          </a-modal>


         <a-modal
            :title="infoImgUploadTitle+' 图片排序'"
            :visible="imgListOperateVisible"
            @ok="onInfoImgSortSave"
            @cancel="closeModal"
            width="80%"
            :maskClosable="true"
            okText="保存"
            cancelText="取消"
            :confirm-loading="infoImgSortSaveLoading"
          >
          <imgListOperate ref="imgListOperateChild" :imgListOperateVisible="imgListOperateVisible" :imgListOperateId="imgListOperateId" :imgListOperateTable="imgListOperateTable" :infoImgSortSaveStatus="infoImgSortSaveStatus"  func="getImgSortInfo" @getImgSortInfo="getImgSortInfo($event)"/>
          </a-modal>


      </span>


        <!-- 产品优势详情批量图片上传-->
     <span slot="infoImgUploadType2" slot-scope="text, record">
         <a-button type="primary" @click="infoImgUploadModelShow(record)" shape="round">
           <a-icon type="cloud-upload" />
          上传
        </a-button>
          /
        <a-button type="primary" @click="imgListOperateVisibleShow(record)" shape="round">
          <a-icon type="setting" />
          设置
        </a-button>
         <a-modal
            :title="infoImgUploadTitle+' 图片上传'"
            :visible="infoImgUploadVisible"
            @ok="onInfoImgUploadSave"
            @cancel="closeModal"
            width="50%"
            okText="保存"
            cancelText="取消"
            :confirm-loading="infoImgUploadSaveLoading"
            :maskClosable="true"
          >
          <auploadList :infoImgUploadVisible="infoImgUploadVisible" :infoImgUploadSaveStatus="infoImgUploadSaveStatus" func="getUploadInfoImgUpload" @getUploadInfoImgUpload="getUploadInfoImgUpload($event)"/>
          </a-modal>


         <a-modal
            :title="infoImgUploadTitle+' 图片设置'"
            :visible="imgListOperateVisible"
            @ok="onInfoImgSortSave"
            @cancel="closeModal"
            width="80%"
            :maskClosable="true"
            okText="保存"
            cancelText="取消"
            :confirm-loading="infoImgSortSaveLoading"
          >
          <imgListOperateTwo ref="imgListOperateChild" :imgListOperateVisible="imgListOperateVisible" :imgListOperateId="imgListOperateId" :imgListOperateTable="imgListOperateTable" :infoImgSortSaveStatus="infoImgSortSaveStatus"  func="getImgSortInfo" @getImgSortInfo="getImgSortInfo($event)"/>
          </a-modal>


      </span>


  </a-table>
  
 </div>
</template>
<script>

import auploadList from './auploadList.vue'
import imgListOperate from './imgListOperate.vue'
import imgListOperateTwo from './imgListOperateTwo.vue'
export default {
  name: 'atable',
  props: {
    tabSourceData:{
      type:Array,
      default:[]
    },
    tabColumns:{
      type:Array,
      default:[]
    },
    buttonEditUrl:{
      type:String,
      default:''
    },
    apiDel:{
      type:String,
      default:''
    },
    apiDelMethod:{
      type:String,
      default:'post'
    },
    // 分页配置 - 支持传入total或完整配置
    pagination: {
      type: [Object, Number],
      default: () => ({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
      })
    },
    // 总记录数 - 可以单独传入
    total: {
      type: Number,
      default: 0
    }
  },
  components: {auploadList, imgListOperate, imgListOperateTwo},
  data() {
    return {
     pageData                       : [],     //列表数据
     formDisplay                    : false,  //表单开关
     imgModelVisible                : false,  //图片弹窗
     configInfoAlertVisible         : false,  //配置弹窗
     htmlContentAlertVisible        : false,  //html内容弹窗
     imgAlertImg                    : '',      //图片路径
     htmlInfoAlertInfo              : '',      //图片路径
     configInfoAlertVisibleText     : '',     //配置弹窗文字
     configInfoAlertVisibleTextType : '',     //配置弹窗类型
     openCloseStatus                : false,  //开关状态

     // 分页内部状态
     currentPage                    : 1,      //当前页码
     currentPageSize                : 10,     //当前页大小

     infoImgUploadTitle             : '',     //批量上传图片内容弹窗标题
     infoImgUploadVisible           : false,  //批量上传图片内容弹窗
     infoImgUploadArray             : [],     //批量上传图片弹窗内容
     infoImgUploadSaveStatus        : false,  //批量上传图片弹窗 保存状态
     infoImgUploadSaveLoading       : false,  //批量上传图片弹窗 保存按钮loading
     infoImgUploadRecord            : null,   //批量上传图片弹窗 保存按钮loading


     imgListOperateVisible          : false,  //图片排序 弹窗显示
     imgListOperateId               : 0,      //图片排序 操作id
     imgListOperateTable            : '',     //图片排序 操作table
     infoImgSortSaveStatus          : false,  //图片排序 按钮保存状态
     infoImgSortSaveLoading         : false,  //图片排序 按钮保存loading
     infoImgSortArray               : []      //图片排序 接受内容
    };
  },
  computed: {
    // 分页配置 - 支持多种传入方式
    paginationConfig() {
      // 如果pagination是数字，则作为total使用
      if (typeof this.pagination === 'number') {
        return {
          current: this.currentPage,
          pageSize: this.currentPageSize,
          total: this.pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
          pageSizeOptions: ['10', '20', '50', '100']
        };
      }

      // 如果单独传入了total prop，优先使用
      const totalCount = this.total > 0 ? this.total : (this.pagination.total || 0);

      // 默认配置
      const defaultConfig = {
        current: this.currentPage,
        pageSize: this.currentPageSize,
        total: totalCount,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
        pageSizeOptions: ['10', '20', '50', '100']
      };

      // 合并用户配置，但保持current和pageSize使用内部状态
      return {
        ...defaultConfig,
        ...this.pagination,
        current: this.currentPage, // 始终使用内部状态
        pageSize: this.currentPageSize, // 始终使用内部状态
        total: totalCount // 确保total优先级最高
      };
    }
  },
  watch:{
    tabSourceData(val){
      this.pageData = val;
    }
  },
  mounted:function () {
    let self = this;

   
  },
  methods: {
    // 处理表格变化（分页、排序、筛选）
    handleTableChange(pagination, filters, sorter) {
      // 更新内部状态
      this.currentPage = pagination.current;
      this.currentPageSize = pagination.pageSize;

      // 发送分页变化事件给父组件
      this.$emit('pageChange', {
        current: pagination.current,
        pageSize: pagination.pageSize,
        filters,
        sorter
      });
    },

    // 重置分页状态（供外部调用）
    resetPagination() {
      this.currentPage = 1;
      this.currentPageSize = 10;
    },

    //删除
   onDelete(id) {
     let self  = this;
     let table = self.$utils.store.get('table');
     self.$utils.http({
        data:{
          'id' : id,
          table: table
        },
        success:(res)=> {
          // 修复：使用正确的消息字段
          self.$message.success(res.msg || res.message || '删除成功');

          // 修复：确保数据过滤正确执行
          console.log('删除前数据量:', self.pageData.length);
          console.log('要删除的ID:', id, '类型:', typeof id);

          // 过滤数据，同时处理字符串和数字类型的ID
          self.pageData = self.pageData.filter(item => {
            const itemId = item.key || item.id;
            const match = itemId != id; // 使用!=而不是!==，处理类型转换
            console.log('项目ID:', itemId, '类型:', typeof itemId, '保留:', match);
            return match;
          });

          console.log('删除后数据量:', self.pageData.length);

          // 如果当前页没有数据了，且不是第一页，则跳转到上一页
          if (self.pageData.length === 0 && self.currentPage > 1) {
            self.currentPage = self.currentPage - 1;
            self.$emit('pageChange', {
              current: self.currentPage,
              pageSize: self.currentPageSize
            });
          }
        },
        fail: (err) => {
          console.error('删除失败:', err);
        }

      }, self.apiDel, self.apiDelMethod)
    },

    //编辑
   onEdit(id) {
     let self      = this;
      let editData = {}
      self.pageData.forEach(element => {
          
          if(element.id == id) {
            editData = element;
          }
      });

      //console.log(editData);
      self.$utils.store.set('editData', editData);
      self.$router.push({path:self.buttonEditUrl, query: {id: id}})
    },

    //打开图片弹窗
    showModal(text) {


      this.imgModelVisible = true;
      this.imgAlertImg     = this.$utils.getHttp()+text;
    },

    ///关闭图片弹窗
    closeModal(e) {
      this.imgModelVisible         = false;
      this.configInfoAlertVisible  = false;
      this.htmlContentAlertVisible = false;
      this.infoImgUploadVisible    = false;
      this.imgAlertImg             = '';
      this.imgListOperateVisible   = false;
    },

    //排序请求
    onDataSortChange(val, id, field) {
      let self = this;

      self.$utils.http({
          data:{
            sort  : val,
            id    : id,
            table : self.$utils.store.get('table'),
            field : field
          },
          success:(res)=> {
            self.$message.success(res.msg || res.message || '操作成功');
            self.$emit('list')

          }
        }, '/api/common/sort') 

    },
    //html弹窗
    htmlInfoAlertShow(text) {
      let self = this;

     self.htmlContentAlertVisible = true;

     self.htmlInfoAlertInfo = text;

    },
    //配置弹窗请求
    configInfoAlertVisibleShow(text, type) {
      let self = this;

     self.configInfoAlertVisible         = true;

     self.configInfoAlertVisibleText     = text;

     if(type == 2) {
       self.configInfoAlertVisibleText   = this.$utils.getHttp()+text
     }
     self.configInfoAlertVisibleTextType = type;

    },
    //开关请求
    onDataSwichChange(val, id, field) {
      let self = this;

      if(val == 1) {
        val = 2;
      } else if (val == 2) {
        val = 1;
      }

      self.$utils.http({
          data:{
            sort  : val,
            id    : id,
            table : self.$utils.store.get('table'),
            field : field
          },
          success:(res)=> {
            self.$message.success(res.msg || res.message || '操作成功');
            self.$emit('list')

          }
        }, '/api/common/sort') 

    },

    //批量上传图片 弹窗显示
    infoImgUploadModelShow(record) {
      let self = this;

      if(record.album_title != undefined) {
        self.infoImgUploadTitle  = record.album_title
      } else if (record.product_name != undefined) {
        self.infoImgUploadTitle  = record.product_name
      } else if (record.detail_title != undefined) {
        self.infoImgUploadTitle  = record.detail_title
      }
      
      self.infoImgUploadVisible  = true;
      self.infoImgUploadArray    = [];
      self.infoImgUploadRecord   = record;

    },
    //批量上传图片接受值
    getUploadInfoImgUpload(saveList) {
      let self = this;
      self.infoImgUploadArray = saveList;

     // console.log(self.infoImgUploadArray);

    },
    //批量上传图片上传接受值
    onInfoImgUploadSave(record) {
      let self = this;
      self.infoImgUploadSaveStatus  = true;
      self.infoImgUploadSaveLoading = true;
      setTimeout(() => {
        let infoImgUploadArray = self.infoImgUploadArray;

        if(infoImgUploadArray.length == 0) {

          self.$message.error('请先上传图片');
          self.infoImgUploadSaveLoading = false;
          self.infoImgUploadSaveStatus  = false;

          return ;
        }

        let table = '';
        if(self.infoImgUploadRecord.album_title != undefined) {
          table = 'album_info';
        } else if (self.infoImgUploadRecord.product_name != undefined) {
          table = 'product_img_info';
        } else if (self.infoImgUploadRecord.detail_title != undefined) {
          table = 'advantage_detail_info';
        }

       // console.log(record);

         self.$utils.http({
            data:{
              infoImg  : infoImgUploadArray,
              parentId : self.infoImgUploadRecord.id,
              table    : table
            },
            success:(res)=> {
              self.$message.success(res.msg || res.message || '操作成功');
              self.infoImgUploadVisible     = false;
              self.infoImgUploadSaveLoading = false;
              self.infoImgUploadSaveStatus  = false;
              self.infoImgUploadRecord      = '';

              self.$emit('list');
            },
            fail:err => {
              self.infoImgUploadSaveLoading = false;
              self.infoImgUploadSaveStatus  = false;
            }

        }, '/api/common/imgInfoUpload')

      }, 1 * 1000)
      

    },


     //图片排序 弹窗显示
    imgListOperateVisibleShow(record) {
      let self = this;

      self.infoImgUploadRecord   = record;
      self.infoImgUploadArray    = [];
      
      self.imgListOperateVisible = true;
      self.imgListOperateId      = record.id;
      if(record.album_title != undefined) {

        self.imgListOperateTable = 'album_info';
        self.infoImgUploadTitle  = record.album_title

      } else if (record.product_name != undefined) {

        self.imgListOperateTable = 'product_img_info';
        self.infoImgUploadTitle  = record.product_name
      } else if (record.detail_title != undefined) {

        self.imgListOperateTable = 'advantage_detail_info';
        self.infoImgUploadTitle  = record.detail_title
      }

    },
    
    //图片批量排序 接受值
    getImgSortInfo(saveList) {
      let self = this;
      self.infoImgSortArray = saveList;
    },

    //图片批量 排序
    onInfoImgSortSave(record) {
      let self = this;
      self.infoImgSortSaveStatus  = true;
      self.infoImgSortSaveLoading = true;
      setTimeout(() => {
        let infoImgSortArray = self.infoImgSortArray;


        let table = '';
        if(self.infoImgUploadRecord.album_title != undefined) {
          table = 'album_info';
        } else if (self.infoImgUploadRecord.product_name != undefined) {
          table = 'product_img_info';
        }  else if (self.infoImgUploadRecord.detail_title != undefined) {
          table = 'advantage_detail_info';
      }

       // console.log(record);

         self.$utils.http({
            data:{
              info     : infoImgSortArray,
              parentId : self.infoImgUploadRecord.id,
              table    : table
            },
            success:res => {
              self.$message.success(res.msg || res.message || '操作成功');
              self.imgListOperateVisible  = false;
              self.infoImgSortSaveLoading = false;
              self.infoImgSortSaveStatus  = false;
              self.infoImgUploadRecord    = '';

              //self.$refs.imgListOperateChild.list();
            },
            fail:err => {
              self.infoImgSortSaveLoading = false;
              self.infoImgSortSaveStatus  = false;
            }

        }, '/api/common/imgInfoEdit')

      }, 1 * 1000)
      

    },



  },
};
</script>
<style lang="less" scoped>
#components-a-popconfirm-demo-placement .ant-btn {
  width: 70px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
  margin-bottom: 8px;
}

// 现代化表格样式
/deep/ .ant-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;

  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    border-bottom: 2px solid #e8e8e8;
    color: #262626;
    font-weight: 600;
    font-size: 14px;
    padding: 16px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    &:hover::before {
      transform: scaleX(1);
    }
  }

  .ant-table-tbody > tr {
    transition: all 0.3s ease;

    &:hover {
      background: rgba(24, 144, 255, 0.04);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
    }

    > td {
      padding: 16px;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.3s ease;

      &:first-child {
        border-left: 3px solid transparent;
        transition: border-left-color 0.3s ease;
      }
    }

    &:hover > td:first-child {
      border-left-color: #1890ff;
    }
  }

  .ant-table-pagination {
    margin: 24px 0 8px 0;
    text-align: center;

    .ant-pagination-item {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      &.ant-pagination-item-active {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
        transform: translateY(-1px);
      }
    }
  }
}

// 操作按钮样式优化
.table-operation {
  display: flex;
  gap: 8px;
  align-items: center;

  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      }
    }

    &.ant-btn-danger {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
      }
    }

    .anticon {
      margin-right: 4px;
    }
  }
}

// 图片查看按钮样式
/deep/ .ant-modal {
  .ant-modal-content {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .ant-modal-header {
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    border-bottom: 1px solid #e8e8e8;
    padding: 20px 24px;

    .ant-modal-title {
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-modal-body {
    padding: 24px;
    text-align: center;

    img {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

// 标签样式优化
/deep/ .ant-tag {
  border-radius: 12px;
  padding: 4px 12px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// 工具提示样式
/deep/ .ant-tooltip {
  .ant-tooltip-content {
    .ant-tooltip-inner {
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

// 确认弹窗样式
/deep/ .ant-popover {
  .ant-popover-content {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    
    .ant-popover-inner {
      border-radius: 8px;
    }

    .ant-popover-inner-content {
      .ant-popconfirm-buttons {
        margin-top: 12px;
        text-align: right;

        .ant-btn {
          border-radius: 6px;
          margin-left: 8px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// 加载状态样式
/deep/ .ant-spin-container {
  .ant-spin {
    .ant-spin-dot {
      .ant-spin-dot-item {
        background-color: #1890ff;
      }
    }
  }
}

// 空状态样式
/deep/ .ant-empty {
  .ant-empty-image {
    opacity: 0.6;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .ant-empty-description {
    color: #999;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-operation {
    flex-direction: column;
    gap: 4px;

    .ant-btn {
      width: 100%;
      font-size: 12px;
    }
  }

  /deep/ .ant-table {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px;
      font-size: 12px;
    }
  }
}
</style>
