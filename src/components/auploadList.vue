
<template>
    <a-upload
    action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
    list-type="picture"
    :multiple="true"
    :file-list="fileList"
    @preview="handlePreview"
    :before-upload="beforeUpload"
    :customRequest="handleUpload"
    @change="handleChange"
    :class="showImgInline?'upload-list-inline':''"
    :remove="handleRemove"
   >
    <a-button> <a-icon type="upload" /> upload </a-button>
  </a-upload>
</template>

<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  name: 'auploadList',
  props: {
    //父类调用方法
    func:{
          type:String,
          default:''
    },
    //来源值
    sourceValue:{
        type:String,
        default:''
    },
    //弹窗状态
    infoImgUploadVisible:{
        type:Boolean,
        default:false
    },
    //图片内联显示状态
    showImgInline:{
      type:Boolean,
      default:false
    },
    //保存按钮状态值
    infoImgUploadSaveStatus:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {  
         //header
      previewVisible: false,    
      previewImage: '',
      fileList: [
        // {
        //   uid: '-1',
        //   name: 'image.png',
        //   status: 'done',
        //   url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
        // },
      ],
      fileNum : 0,
      
    };
  },
  watch:{
    infoImgUploadVisible:function(val){

        this.fileList = [];
        this.fileNum  = 0;
    },
    infoImgUploadSaveStatus:function(val) {
      let self = this;
        if(val == true) {

          let saveList = [];

          self.fileList.forEach(element => {
              if(element.status == 'done') {
                saveList.push({fileSrc : element.originFile, fileName:element.name});
              }
          });

          
          self.$emit(self.func, saveList);
        }
    }
  },
  mounted:function () {
    let self = this;

    if(self.sourceValue != ''){
      let fileList = [
        {
          uid: '-1',
          name: self.sourceValue,
          status: 'done',
          url: self.sourceValue,
        },
      ]
      
      self.fileList = fileList;
    }
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    handleChange(info) {
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, imageUrl => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },

    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
    },

    beforeUpload(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        this.$message.error('请上传jpg 或者png格式');
      }
      const isLt2M = file.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error('图片上传最大限制 10M !');
      }
      return isJpgOrPng && isLt2M;
    },

    handleUpload(option) {
      let self = this;
      //console.log(option);
      // const formData = new FormData();
      // fileList.forEach(file => {
      //   formData.append('files[]', file);
      // });

      // this.uploading = true;

      let params = new FormData();
      params.append("upFile", option.file);

      this.$utils.http({
            data:params,
            headers:{
              'token'        : self.$utils.store.getS('token'),
              'Content-Type' : 'multipart/form-data',

            },
            success:(res)=> {
              let data          = res.data;
              // self.sortListData = data;
              self.$message.success(res.message);

              //console.log(res);


              let fileList = {
                  uid: self.fileNum,
                  name: option.file.name,
                  status: 'done',
                  url: data.httpFile,
                  originFile: data.originFile,
                };
              
              
              self.fileList.push(fileList);


             

              self.fileNum++;

            }
          }, '/api/common/imgUpload')
    },
  },
};
</script>

<style scoped>
/* tile uploaded pictures */
.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}
.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}
.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}
</style>