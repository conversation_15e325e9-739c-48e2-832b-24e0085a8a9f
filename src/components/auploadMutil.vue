
<template>
  <div class="clearfix">
    <a-upload
      :action="action"
      list-type="picture-card"
      :file-list="fileList"
      @preview="handlePreview"
      :name="upName"
      :headers="headers"
      :before-upload="beforeUpload"
      :customRequest="handleUpload"
      @change="handleChange"
      
    >
      <div v-if="fileList.length < uploadLength">
        <a-icon type="plus" />
        <div class="ant-upload-text">
          Upload
        </div>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel" :zIndex="99999">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  name: 'auploadMutil',
  props: {
    //父类调用方法
    func:{
          type:String,
          default:''
    },
    //来源值
    sourceValue:{
        type:Array|String,
        default:[]
    },
    uploadLength:{
      type:Number,
      default:1
    }
  },
  data() {
    return {  
      upName: 'upFile',                                      //请求名称
      action: this.$utils.getHttp()+'/api/common/imgUpload', //请求地址
      headers: {
         'token' : this.$utils.store.getS('token')
      },            //header
      previewVisible: false,    
      previewImage: '',
      fileList: [
        // {
        //   uid: '-1',
        //   name: 'image.png',
        //   status: 'done',
        //   url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
        // },
      ],
      fileNum : 0
    };
  },
  watch:{
    
  },
  mounted:function () {
    let self = this;

    if(self.sourceValue != ''){
      //let jsonData = JSON.parse(self.sourceValue);

      let fileList = [];
      self.sourceValue.forEach((value, index) => {
          fileList.push({
              uid: index,
              name: value,
              status: 'done',
              url: self.$utils.getHttp()+value
            })
      });
      
      self.fileNum  = self.sourceValue.length;
      self.fileList = fileList;
    }
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    handleChange(info) {
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, imageUrl => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },

    // handleChange({ fileList }) {
    //   this.fileList = fileList;
    // },

    beforeUpload(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        this.$message.error('请上传jpg 或者png格式');
      }
      const isLt2M = file.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error('图片上传最大限制 10M !');
      }
      return isJpgOrPng && isLt2M;
    },

    handleUpload(option) {
      let self = this;
      //console.log(option);
      // const formData = new FormData();
      // fileList.forEach(file => {
      //   formData.append('files[]', file);
      // });

      // this.uploading = true;

      let params = new FormData();
      params.append("upFile", option.file);

      this.$utils.http({
            data:params,
            headers:{
              'token'        : self.$utils.store.getS('token'),
              'Content-Type' : 'multipart/form-data',

            },
            success:(res)=> {
              let data          = res.data;
              // self.sortListData = data;
              self.$message.success(res.message);

              //console.log(res);


              let fileList =
                {
                  uid: self.fileNum,
                  name: option.file.name,
                  status: 'done',
                  url: data.httpFile,
                }
              
              
              self.fileList.push(fileList);


              self.$emit(self.func, data.originFile);

              self.fileNum++;

            }
          }, '/api/common/imgUpload')
    },
  },
};
</script>

<style scoped>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>