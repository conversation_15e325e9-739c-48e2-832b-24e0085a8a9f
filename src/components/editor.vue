<template>
    <div>
        <div ref="editor" style="text-align:left"></div>
    </div>
</template>

<script>
    import E from 'wangeditor'
    

    export default {
      name: 'editor',
      data () {
        return {
          editorContent: ''
        }
      },
      props: {
        //父类调用方法
        func:{
          type:String,
          default:''
        },
        //来源值
        sourceValue:{
          type:String,
          default:''
        }
      },
      methods: {
        getContent: function () {
            
        }
      },
      mounted() {
        let self   = this;
        let editor = new E(this.$refs.editor)
        
        editor.customConfig.onchange = (html) => {
          self.$emit(self.func, html)
        }

        // 配置服务器端地址
         editor.customConfig.uploadImgServer = self.$utils.getHttp()+'/api/common/imgUpload'
         editor.customConfig.uploadFileName = 'upFile'

         editor.customConfig.uploadImgHeaders = {
            'token'        : self.$utils.store.getS('token')
        }

          // 自定义菜单配置
        editor.customConfig.menus = [
            'head',  // 标题
            'bold',  // 粗体
            'fontSize',  // 字号
            'fontName',  // 字体
            'italic',  // 斜体
            'underline',  // 下划线
            'strikeThrough',  // 删除线
            'foreColor',  // 文字颜色
            'backColor',  // 背景颜色
            'link',  // 插入链接
            'list',  // 列表
            'justify',  // 对齐方式
            'quote',  // 引用
            'emoticon',  // 表情
            'image',  // 插入图片
            'table',  // 表格
            //'video',  // 插入视频
            //'code',  // 插入代码
            'undo',  // 撤销
            'redo'  // 重复
        ]

        editor.customConfig.uploadImgHooks = {
          before: function (xhr, editor, files) {
              // 图片上传之前触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，files 是选择的图片文件
              
              // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传
              // return {
              //     prevent: true,
              //     msg: '放弃上传'
              // }
              self.$message.loading('loading', 2.5);
          },
          success: function (xhr, editor, result) {
              // 图片上传并返回结果，图片插入成功之后触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
               self.$message.destroy();
               self.$message.success(result.message);

          },
          fail: function (xhr, editor, result) {
              // 图片上传并返回结果，但图片插入错误时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
              self.$message.destroy();
              self.$message.error(result.message)
          },
          error: function (xhr, editor, result) {
              // 图片上传出错时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
              let response = JSON.parse(xhr.response);
              self.$message.destroy();
              self.$message.error(response.message)
          },
          timeout: function (xhr, editor) {
              // 图片上传超时时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
          },

          // 如果服务器端返回的不是 {errno:0, data: [...]} 这种格式，可使用该配置
          // （但是，服务器端返回的必须是一个 JSON 格式字符串！！！否则会报错）
          customInsert: function (insertImg, result, editor) {
              // 图片上传并返回结果，自定义插入图片的事件（而不是编辑器自动插入图片！！！）
              // insertImg 是插入图片的函数，editor 是编辑器对象，result 是服务器端返回的结果

              // 举例：假如上传图片成功后，服务器端返回的是 {url:'....'} 这种格式，即可这样插入图片：
              var url = result.data.httpFile
              insertImg(url)

              // result 必须是一个 JSON 格式字符串！！！否则报错
          }
      }

      editor.customConfig.customAlert = function (info) {
          
      }
      

        // 进行下文提到的其他配置
        editor.create()

        if(self.sourceValue != ''){

            editor.txt.html(self.sourceValue)
        }
      }
    }
</script>

<style scoped>
</style>
