<template>
 <div>
   
    <a-row type="flex" class="row_title">
      <a-col :span="2" :order="2">
            序号
      </a-col>
      <a-col :span="4" :order="1">
            类型
      </a-col>
      <a-col :span="8" :order="3">
            文件名
      </a-col>
      <a-col :span="3" :order="4">
            图片
      </a-col>
  
      <a-col :span="3" :order="5">
            查看
      </a-col>
     
      <a-col :span="3" :order="6">
            操作
      </a-col>
    </a-row>
    <a-row type="flex" class="row_content" v-for="(item, index) in imgArr" :key="item.id">
        <a-col :span="2" :order="2">
         <a-input-number v-model="item.info_num" :min="0" :max="99999" @change="onChange" />
        </a-col>
        <a-col :span="4" :order="1">
          <a-radio-group v-model="item.info_type" button-style="solid">
              <a-radio-button :value="1">
                  小图
              </a-radio-button>
              <a-radio-button :value="2">
                  大图
              </a-radio-button>
          </a-radio-group>
        </a-col>

        <a-col :span="8" :order="3">
            <p>{{item.info_img_filename}}</p>
        </a-col>

        <a-col :span="3" :order="4">
          <img :src="item.showImg">
        </a-col>
        <a-col :span="3" :order="5">
              <a-button type="primary" @click="showAlert(item.showImg)" size="small">
                查看
              </a-button>
        </a-col>
      
        <a-col :span="3" :order="6">
              <!-- <a-button type="primary" @click="reStore(index)" size="small">
                  恢复
              </a-button>
              / -->
              <a-button type="danger" @click="del(index)" size="small">
                  删除
              </a-button>
        </a-col>
    </a-row>

    <a-modal v-model="visible" title="图片查看" width="60%" :footer="null">
      <img :src="alertImg" style="width:100%;height:100%;">
    </a-modal>

  </div>
</template>

<script>
export default {
  name: 'imgListOperate',
  props: {
    //父类调用方法
    func:{
          type:String,
          default:''
    },
    imgListOperateId: {
      type:Number,
      default:0
    },
    imgListOperateTable: {
      type:String,
      default:''
    },
    imgListOperateVisible: {
      type:Boolean,
      default:false
    },
    //保存按钮状态值
    infoImgSortSaveStatus:{
      type:Boolean,
      default:false
    }
  },
  watch:{
      imgListOperateVisible:function(val) {
         $('.ant-modal-body').css({'height':'80px'});
        if(val == true) {
           this.list();
        }
       
      },
      infoImgSortSaveStatus:function(val) {
      let self = this;
        if(val == true) {

          self.imgArr.forEach(element => {
            delete(element.showImg)
          });

          self.$emit(self.func, self.imgArr);
          
        }
    }
  },
  mounted:function () {
    let self = this;

    self.list();
  },

  data() {
    return {
      sortNum  : [],
      visible  : false,
      alertImg : false,
      isShow   : false,
      imgArr   : [
        // {
        //   imgSrc    : 'https://images5.alphacoders.com/944/thumb-1920-944326.png',
        //   file_name : 'https://images5.alphacoders.com/944/thumb-1920-944326.png',
        //   sortNum   :  '',
        //   type      :  0,
        //   id        :  1
        // },
        // {
        //   imgSrc  : 'https://lh3.googleusercontent.com/proxy/L87caID-PwbumHHkIgtAagEUxiXubR18gq42B_m-oGkn3MQmgHsY8SL2OibWtPWMFXSQK1jATWenB6l54GDv-s_PTfnEYIDlHZzrp9gDGgEeRz7HpAjDSbqhkj6mKQ',
        //   file_name  : 'https://lh3.googleusercontent.com/proxy/L87caID-PwbumHHkIgtAagEUxiXubR18gq42B_m-oGkn3MQmgHsY8SL2OibWtPWMFXSQK1jATWenB6l54GDv-s_PTfnEYIDlHZzrp9gDGgEeRz7HpAjDSbqhkj6mKQ',
        //   sortNum :  '',
        //   type    :  0,
        //   id      :  2
        // },
        // {
        //   imgSrc  : 'https://lh3.googleusercontent.com/proxy/L87caID-PwbumHHkIgtAagEUxiXubR18gq42B_m-oGkn3MQmgHsY8SL2OibWtPWMFXSQK1jATWenB6l54GDv-s_PTfnEYIDlHZzrp9gDGgEeRz7HpAjDSbqhkj6mKQ',
        //   file_name  : 'https://lh3.googleusercontent.com/proxy/L87caID-PwbumHHkIgtAagEUxiXubR18gq42B_m-oGkn3MQmgHsY8SL2OibWtPWMFXSQK1jATWenB6l54GDv-s_PTfnEYIDlHZzrp9gDGgEeRz7HpAjDSbqhkj6mKQ',
        //   sortNum :  '',
        //   type    :  0,
        //   id      :  3
        // },
        // {
        //   imgSrc  : 'https://images5.alphacoders.com/944/thumb-1920-944326.png',
        //   file_name  : 'https://images5.alphacoders.com/944/thumb-1920-944326.png',
        //   sortNum :  '',
        //   type    :  0,
        //   id      :  4
        // }
      ]
    };
  },
  methods: {
    onChange(value) {
      //console.log(value);
    },
    showAlert(src) {
        this.visible  = true;
        this.alertImg = src;
    },
    //行恢复
    itemRestore(index) {
      let self = this;
      self.imgArr[index].sortNum = '';
      self.imgArr[index].type    = 0;
    },
    //删除
    del(index) {
      let self = this;

      self.imgArr.splice(index, 1);
    },

    //列表排序
    listSort(index) {
      let self    = this;
      self.isShow = true;
    },
    //列表恢复
    listRestore() {
      let self = this;

      self.imgArr.forEach(element => {
        element.sortNum = '';
        element.type    = 0;
      });

      self.isShow = false;

    },

    //列表
    list() {
        let self = this;
         $('.ant-modal-body').css({
                                    'height':'500px',
                                    'overflow-y':'scroll',
                                    'overflow-x':'hidden',
                                }).hide();
        self.$utils.http({
            data:{
              table    : self.imgListOperateTable,
              parentId : self.imgListOperateId
            },
            success:(res)=> {
              let data = res.data;



              data.forEach(element => {
                element.key     = element.id,
                element.showImg = self.$utils.getHttp()+element.info_img_src
              });

              self.imgArr = data;

              setTimeout(() => {
                $('.ant-modal-body').show();
              }, 0.5* 1000)

            }
          }, '/api/common/imgInfoList')
    }
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.row_title{
    padding-bottom : 10px;
    width: 95%;
    margin: 0 auto;
    text-align: center;
}

.row_content{
    border-bottom  : 1px solid #eee;
    padding-bottom : 10px;
    width: 95%;
    margin: 0 auto;
    line-height: 60px;
    text-align: center;
}
.row_content img {
    width:60px;
    height:60px;
    border: 1px solid #eee;
    padding: 5px;
}

.row_bottom{
    padding-top : 10px;
    padding-bottom : 10px;
    width: 95%;
    margin: 0 auto;
}
p{position: relative; line-height: 40px; max-height: 40px;overflow: hidden;top: 10px;left: 10px;}
p::after{content: "..."; position: absolute; bottom: 0; right: 0; padding-left: 40px;
  background: -webkit-linear-gradient(left, transparent, #fff 55%);
  background: -o-linear-gradient(right, transparent, #fff 55%);
  background: -moz-linear-gradient(right, transparent, #fff 55%);
  background: linear-gradient(to right, transparent, #fff 55%);
}
/*控制整个滚动条*/
::-webkit-scrollbar {
    background-color: lightgray;
    width: 10px;
    height: 10px;
    background-clip: padding-box;
}

/*滚动条两端方向按钮*/
::-webkit-scrollbar-button {
    background-color: pink;
}

/*滚动条中间滑动部分*/
::-webkit-scrollbar-thumb {
    background-color: blue;
    border-radius: 5px;
}

/*滚动条右下角区域*/
::-webkit-scrollbar-corner {
    background-color: red;
}

</style>
