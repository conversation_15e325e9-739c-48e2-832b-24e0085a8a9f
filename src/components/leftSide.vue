<template>
  <a-layout-sider width="200" class="custom-sider">
    <a-menu
      mode="inline"
      :default-selected-keys="[selected]"
      :default-open-keys="[open]"
      class="custom-menu"
    >
    
    <a-sub-menu key="sub1" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="idcard" class="menu-icon" />
        <span>管理员</span>
      </span>
      <a-menu-item key="admin" class="menu-item">
        <router-link to="/" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub2" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="tool" class="menu-icon" />
        <span>配置</span>
      </span>
      <a-menu-item key="configSort" class="menu-item">
        <router-link :to="{ name: 'configSort'}" class="menu-link">分类</router-link>
      </a-menu-item>
      <a-menu-item key="configList" class="menu-item">
        <router-link :to="{ name: 'configList'}" class="menu-link">列表</router-link>
      </a-menu-item>
      <a-menu-item key="configBanner" class="menu-item">
        <router-link :to="{ name: 'configBanner'}" class="menu-link">幻灯片</router-link>
      </a-menu-item>
      <a-menu-item key="configProductFirst" class="menu-item">
        <router-link :to="{ name: 'configProductFirst'}" class="menu-link">首页产品 第一版</router-link>
      </a-menu-item>
      <a-menu-item key="configProductSecond" class="menu-item">
        <router-link :to="{ name: 'configProductSecond'}" class="menu-link">首页产品 第二版</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub3" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="picture" class="menu-icon" />
        <span>相册</span>
      </span>
      <a-menu-item key="albumSort" class="menu-item">
        <router-link :to="{ name: 'albumSort'}" class="menu-link">分类</router-link>
      </a-menu-item>
      <a-menu-item key="album" class="menu-item">
        <router-link :to="{ name: 'album'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub4" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="compass" class="menu-icon" />
        <span>产品优势</span>
      </span>
      <a-menu-item key="advantage" class="menu-item">
        <router-link :to="{ name: 'advantage'}" class="menu-link">列表</router-link>
      </a-menu-item>
      <a-menu-item key="advantageDetails" class="menu-item">
        <router-link :to="{ name: 'advantageDetails'}" class="menu-link">详情</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub5" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="twitter" class="menu-icon" />
        <span>新闻</span>
      </span>
      <a-menu-item key="newsSort" class="menu-item">
        <router-link :to="{ name: 'newsSort'}" class="menu-link">分类</router-link>
      </a-menu-item>
      <a-menu-item key="newsList" class="menu-item">
        <router-link :to="{ name: 'newsList'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub6" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="bulb" class="menu-icon" />
        <span>常见问题</span>
      </span>
      <a-menu-item key="qaList" class="menu-item">
        <router-link :to="{ name: 'qaList'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub7" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="contacts" class="menu-icon" />
        <span>服务政策</span>
      </span>
      <a-menu-item key="serviceList" class="menu-item">
        <router-link :to="{ name: 'serviceList'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub8" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="exception" class="menu-icon" />
        <span>投诉建议</span>
      </span>
      <a-menu-item key="suggestList" class="menu-item">
        <router-link :to="{ name: 'suggestList'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub9" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="audit" class="menu-icon" />
        <span>品牌加盟</span>
      </span>
      <a-menu-item key="joinList" class="menu-item">
        <router-link :to="{ name: 'joinList'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>

    <a-sub-menu key="sub10" class="menu-submenu">
      <span slot="title" class="menu-title">
        <a-icon type="shopping-cart" class="menu-icon" />
        <span>产品</span>
      </span>
      <a-menu-item key="productSort" class="menu-item">
        <router-link :to="{ name: 'productSort'}" class="menu-link">分类</router-link>
      </a-menu-item>
      <a-menu-item key="productScenes" class="menu-item">
        <router-link :to="{ name: 'productScenes'}" class="menu-link">场景</router-link>
      </a-menu-item>
      <a-menu-item key="product" class="menu-item">
        <router-link :to="{ name: 'product'}" class="menu-link">列表</router-link>
      </a-menu-item>
    </a-sub-menu>
    </a-menu>
  </a-layout-sider>
</template>
<script>
export default {
  name: 'leftSide',
  props: {
    selected:{
      type:String,
      default:''
    },
    open:{
      type:String,
      default:''
    }
  },
  data () {
    return {
      
    }
  },
  created () {
    
  },
  methods: {
    
  },
  watch: {
    
  }
}
</script>
<style lang="less" scoped>
.custom-sider {
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  border-right: 1px solid #e8e8e8;
}

.custom-menu {
  height: 100%;
  border-right: none;
  background: transparent;
  padding: 16px 0;

  .menu-submenu {
    margin-bottom: 4px;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      background: rgba(24, 144, 255, 0.06);
    }

    /deep/ .ant-menu-submenu-title {
      padding: 12px 24px !important;
      margin: 0;
      border-radius: 8px;
      height: auto !important;
      line-height: 1.5 !important;
      display: flex !important;
      align-items: center !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(24, 144, 255, 0.08);
        color: #1890ff;
      }
    }

    /deep/ .ant-menu-submenu-open .ant-menu-submenu-title {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }
  }

  .menu-title {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 14px;

    .menu-icon {
      margin-right: 12px;
      font-size: 16px;
    }
  }

  .menu-item {
    margin: 0;
    padding: 0;
    border-radius: 6px;

    &:hover {
      background: rgba(24, 144, 255, 0.08);
    }

    /deep/ .ant-menu-item-selected {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border-radius: 6px;
      
      .menu-link {
        color: white !important;
      }

      &::after {
        display: none;
      }
    }

    .menu-link {
      display: flex;
      align-items: center;
      padding: 10px 16px 10px 48px;
      color: #666;
      text-decoration: none;
      font-size: 13px;
      border-radius: 6px;
      line-height: 1.4;
      min-height: 36px;

      &:hover {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.06);
      }
    }
  }

  /deep/ .ant-menu-submenu-inline .ant-menu-item {
    padding: 0 !important;
    margin: 2px 12px;
    width: calc(100% - 24px);
    height: auto !important;
    line-height: 1.5 !important;
    
    .ant-menu-item-selected {
      height: auto !important;
    }
  }

  /deep/ .ant-menu-item-selected .menu-link {
    color: white !important;
    background: transparent;
  }
}

// 选中状态的特殊处理
/deep/ .ant-menu-item-selected {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  
  &::after {
    display: none !important;
  }
}

// 展开子菜单样式
/deep/ .ant-menu-submenu-inline > .ant-menu {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 0 0 8px 8px;
  margin-top: 4px;
  padding: 8px 0;
}
</style>
