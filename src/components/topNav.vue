<template>
  <a-layout-header class="custom-header">
    <div class="header-content">
      <div class="brand-section">
        <div class="brand-icon">
          <a-icon type="shield" />
        </div>
        <h3 class="brand-title">疗愈后台管理</h3>
      </div>
      <div class="header-actions">
        <a-dropdown placement="bottomRight">
          <div class="user-info">
            <a-avatar class="user-avatar" icon="user" />
            <span class="user-name">管理员</span>
            <a-icon type="down" class="dropdown-icon" />
          </div>
          <a-menu slot="overlay" class="user-dropdown">
            <!-- <a-menu-item key="profile">
              <a-icon type="user" />
              个人信息
            </a-menu-item>
            <a-menu-item key="settings">
              <a-icon type="setting" />
              系统设置
            </a-menu-item>
            <a-menu-divider /> -->
            <a-menu-item key="logout" @click="logout">
              <a-icon type="logout" />
              退出登录
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
    </div>
  </a-layout-header>
</template>
<script>
export default {
  name: 'topNav',
  props: {
    
  },
  data () {
    return {
     
    }
  },
  methods: {
    logout () {

      this.$utils.store.clearS();

      this.$router.push({ path: '/login' });
      this.$message.success('已退出登录');
    }
  }
}
</script>
<style lang="less" scoped>
.custom-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  position: relative;
  z-index: 10;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    position: relative;
    z-index: 1;
  }

  .brand-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .brand-icon {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }

      .anticon {
        color: white;
        font-size: 20px;
      }
    }

    .brand-title {
      color: white;
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      letter-spacing: 0.5px;
    }
  }

  .header-actions {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .user-avatar {
        background: rgba(255, 255, 255, 0.9);
        color: #1890ff;
        border: 2px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }

      .user-name {
        color: white;
        font-weight: 500;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .dropdown-icon {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        transition: all 0.3s ease;
      }

      &:hover .dropdown-icon {
        color: white;
        transform: rotate(180deg);
      }
    }
  }
}

// 下拉菜单样式
/deep/ .user-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  overflow: hidden;

  .ant-dropdown-menu-item {
    padding: 12px 16px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(24, 144, 255, 0.06);
      color: #1890ff;
    }

    .anticon {
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .ant-dropdown-menu-item-divider {
    margin: 4px 0;
    background: #f0f0f0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-header {
    padding: 0 16px;

    .brand-section {
      .brand-title {
        font-size: 16px;
      }
    }

    .header-actions {
      .user-info {
        padding: 6px 12px;

        .user-name {
          display: none;
        }
      }
    }
  }
}
</style>
