import Vue from 'vue'
import App from './App.vue'
import router from './router'
import './plugins/ant-design-vue.js'


import Vuex from 'vuex'
Vue.use(Vuex);


import {message} from 'ant-design-vue';
Vue.use(message);
Vue.prototype.$message = message;

import utils from '@/utils'
Vue.prototype.$utils  = utils;

import { FormModel } from 'ant-design-vue';
Vue.use(FormModel);

import $ from 'jquery'

Vue.config.productionTip = false
new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
