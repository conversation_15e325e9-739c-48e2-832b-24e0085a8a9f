import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Login.vue'
import adminIndex from '../views/admin/Index.vue'
import adminEdit from '../views/admin/Edit.vue'
import ConfigSort from '../views/config/Sort.vue'
import ConfigSortEdit from '../views/config/SortEdit.vue'
import ConfigList from '../views/config/List.vue'
import ConfigListEdit from '../views/config/ListEdit.vue'
import ConfigBanner from '../views/config/Banner.vue'
import ConfigBannerEdit from '../views/config/BannerEdit.vue'

import albumSort from '../views/album/Sort.vue'
import albumSortEdit from '../views/album/SortEdit.vue'

import albumIndex from '../views/album/Index.vue'
import ListEdit from '../views/album/ListEdit.vue'


import advantage from '../views/advantage/Index.vue'
import advantageEdit from '../views/advantage/ListEdit.vue'


import advantageDetails from '../views/advantage/Details.vue'
import advantageDetailsEdit from '../views/advantage/DetailsEdit.vue'



import newsSort from '../views/news/Sort.vue'
import newsSortEdit from '../views/news/SortEdit.vue'
import newsList from '../views/news/List.vue'
import newsListEdit from '../views/news/ListEdit.vue'


import QAList from '../views/qa/List.vue'
import QAListEdit from '../views/qa/ListEdit.vue'


import serviceList from '../views/service/List.vue'
import serviceListEdit from '../views/service/ListEdit.vue'


import suggestList from '../views/suggest/List.vue'
import joinList    from '../views/join/List.vue'


import productSort    from '../views/product/Sort.vue'
import productSortEdit from '../views/product/SortEdit.vue'


import productScenes    from '../views/product/Scenes.vue'
import productScenesEdit from '../views/product/ScenesEdit.vue'


import product     from '../views/product/List.vue'
import productEdit from '../views/product/ListEdit.vue'


import configProductFirst     from '../views/config/ProductFirst.vue'
import configProductFirstEdit from '../views/config/ProductFirstEdit.vue'


import configProductSecond     from '../views/config/ProductSecond.vue'
import configProductSecondEdit from '../views/config/ProductSecondEdit.vue'

import utils from '../utils'

Vue.use(VueRouter)

  const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Home,
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/',
    name: 'index',
    component: adminIndex,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/adminEdit',
    name: 'edit',
    component: adminEdit,
    meta: {
      title: '编辑'
    }
  },
  {
    path: '/configSort',
    name: 'configSort',
    component: ConfigSort,
    meta: {
      title: '配置分类'
    }
  },
  {
    path: '/configSortEdit',
    name: 'configSortEdit',
    component: ConfigSortEdit,
    meta: {
      title: '配置分类编辑'
    }
  },
  {
    path: '/configList',
    name: 'configList',
    component: ConfigList,
    meta: {
      title: '配置列表'
    }
  },
  {
    path: '/configListEdit',
    name: 'configListEdit',
    component: ConfigListEdit,
    meta: {
      title: '配置列表编辑'
    }
  },
  {
    path: '/configBanner',
    name: 'configBanner',
    component: ConfigBanner,
    meta: {
      title: '幻灯片'
    }
  },
  {
    path: '/configBannerEdit',
    name: 'configBannerEdit',
    component: ConfigBannerEdit,
    meta: {
      title: '幻灯片编辑'
    }
  },
  {
    path: '/album/sort',
    name: 'albumSort',
    component: albumSort,
    meta: {
      title: '相册分类'
    }
  },
  {
    path: '/album/sort/edit',
    name: 'albumSortEdit',
    component: albumSortEdit,
    meta: {
      title: '相册分类编辑'
    }
  },
  {
    path: '/album/index',
    name: 'album',
    component: albumIndex,
    meta: {
      title: '相册列表'
    }
  },
  {
    path: '/album/edit',
    name: 'ListEdit',
    component: ListEdit,
    meta: {
      title: '相册列表编辑'
    }
  },
  {
    path: '/advantage/index',
    name: 'advantage',
    component: advantage,
    meta: {
      title: '产品优势'
    }
  },
  {
    path: '/advantage/edit',
    name: 'advantageEdit',
    component: advantageEdit,
    meta: {
      title: '产品优势编辑'
    }
  },
  {
    path: '/advantage/detail',
    name: 'advantageDetails',
    component: advantageDetails,
    meta: {
      title: '产品优势详情优势'
    }
  },
  {
    path: '/advantage/detailEdit',
    name: 'advantageDetailsEdit',
    component: advantageDetailsEdit,
    meta: {
      title: '产品优势编辑'
    }
  },
  {
    path: '/news/sort',
    name: 'newsSort',
    component: newsSort,
    meta: {
      title: '新闻分类'
    }
  },
  {
    path: '/news/sortEdit',
    name: 'newsSortEdit',
    component: newsSortEdit,
    meta: {
      title: '新闻分类编辑'
    }
  },
  {
    path: '/news/list',
    name: 'newsList',
    component: newsList,
    meta: {
      title: '新闻列表'
    }
  },
  {
    path: '/news/ListEdit',
    name: 'newsListEdit',
    component: newsListEdit,
    meta: {
      title: '新闻编辑'
    }
  },
  {
    path: '/qa/list',
    name: 'qaList',
    component: QAList,
    meta: {
      title: '常见问题'
    }
  },
  {
    path: '/qa/ListEdit',
    name: 'qaListEdit',
    component: QAListEdit,
    meta: {
      title: '常见问题编辑'
    }
  },
  {
    path: '/service/list',
    name: 'serviceList',
    component: serviceList,
    meta: {
      title: '服务政策'
    }
  },
  {
    path: '/service/list/edit',
    name: 'serviceListEdit',
    component: serviceListEdit,
    meta: {
      title: '服务政策编辑'
    }
  },
  {
    path: '/suggest/list',
    name: 'suggestList',
    component: suggestList,
    meta: {
      title: '投诉建议'
    }
  },
  {
    path: '/join/list',
    name: 'joinList',
    component: joinList,
    meta: {
      title: '加盟'
    }
  },
  {
    path: '/product/sort',
    name: 'productSort',
    component: productSort,
    meta: {
      title: '产品分类'
    }
  },
  {
    path: '/product/sort/edit',
    name: 'productSortEdit',
    component: productSortEdit,
    meta: {
      title: '产品分类编辑'
    }
  },
  {
    path: '/product/scenes',
    name: 'productScenes',
    component: productScenes,
    meta: {
      title: '产品场景'
    }
  },
  {
    path: '/product/scenes/edit',
    name: 'productScenesEdit',
    component: productScenesEdit,
    meta: {
      title: '产品场景编辑'
    }
  },
  {
    path: '/product/list',
    name: 'product',
    component: product,
    meta: {
      title: '产品'
    }
  },
  {
    path: '/product/list/edit',
    name: 'productEdit',
    component: productEdit,
    meta: {
      title: '产品编辑'
    }
  },
  {
    path: '/config/productFirst',
    name: 'configProductFirst',
    component: configProductFirst,
    meta: {
      title: '首页产品 第一版'
    }
  },
  {
    path: '/config/productFirst/edit',
    name: 'configProductFirstEdit',
    component: configProductFirstEdit,
    meta: {
      title: '首页产品 第一版编辑'
    }
  },
  {
    path: '/config/productSecond',
    name: 'configProductSecond',
    component: configProductSecond,
    meta: {
      title: '首页产品 第二版'
    }
  },
  {
    path: '/config/productSecond/edit',
    name: 'configProductSecondEdit',
    component: configProductSecondEdit,
    meta: {
      title: '首页产品 第二版编辑'
    }
  },

]




const router = new VueRouter({
  routes
})


/** 
 * to表示即将进入的页面路由，
 * from表示当前导航正要离开的路由
 * next: Function:执行效果依赖 next 方法的调用参数。
 * next(): 进行管道中的下一个钩子。如果全部钩子执行完了，则导航的状态就是 confirmed （确认的）。
 * next(false): 中断当前的导航。如果浏览器的 URL 改变了（可能是用户手动或者浏览器后退按钮），那么 URL 地址会重置到 from 路由对应的地址。
 * next('/') 或者 next({ path: '/' }): 跳转到一个不同的地址。当前的导航被中断，然后进行一个新的导航。
 * next(error): (2.4.0+) 如果传入 next 的参数是一个 Error 实例，则导航会被终止且该错误会被传递给 router.onError() 注册过的回调。
*/
router.beforeEach((to, from, next) => {
  //console.log(to);
  //console.log(from);
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title
  };
  const isLogin = utils.store.getS("token");
  if (isLogin){
    next();
  }else{
    if (to.fullPath == "/login") {
      next();
    }else{
      next({path: '/login'})
    };
  };
})


export default router
