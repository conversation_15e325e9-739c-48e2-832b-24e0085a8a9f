import Vue from 'vue';
import Axios from 'axios';
import {message} from 'ant-design-vue';


// 开发环境使用代理，生产环境使用完整URL
const isDev = process.env.NODE_ENV === 'development';
const http = isDev ? '/api' : 'http://192.168.51.202:8081';

const utils = {

  // 获取域名地址
  getHttp () {
    
   return http;     

  },

  // 时间戳转日期
  getDate (time) {
    
   return new Date(parseInt(time) * 1000).toLocaleString().replace(/:\d{1,2}$/,' ');     

  },

  // 获取节点
  query (dom) {
    const obj = document.querySelectorAll(dom);
    return obj;
  },

  // 弹出提示
  showToast ({
    text,
    time = 2000,
    callback
  }) {
    this.query('.showHint').forEach(item => {
      item.style.display = "none";
    })
    if (typeof callback === 'function') callback(res);
    const p = document.createElement('p');
    p.innerHTML = text;
    p.className = 'showHint'
    p.style.cssText =
                `
        position:fixed;
        top:50%;
        left:50%;
        transform:translate(-50%,-50%);
        background:rgba(0,0,0,0.5);
        min-width:60px;
        line-height:34px;
        text-align:center;
        border-radius:5px;
        color:#fff;
        font-size:14px;
        padding:0 30px;
        opacity: 0;
        transition:0.4s;
        -webkit-user-select: none;
        user-select: none;
        z-index:9999999999999999999999;
        `;
    this.query('body')[0].appendChild(p);
    return new Promise(resolve => {
      setTimeout(resolve, 20);
    }).then(() => {
      p.style.cssText += `opacity: 1;`;
      return new Promise(resolve => {
        setTimeout(resolve, time);
      })
    }).then(() => {
      p.style.cssText += `opacity: 0;`;
      return new Promise(resolve => {
        setTimeout(resolve, 400);
      })
    }).then(() => {
      this.query('body')[0].removeChild(p);
      return new Promise(resolve => {
        resolve();
      })
    })
  },
  // 等待加载动画
  loadingMove ({
    color,
    size,
    width,
    speed,
    timeout
  }) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext("2d");
    const gradual = ctx.createLinearGradient(0, size, size, size);
    let rotate = 0;
    canvas.width = canvas.height = size;
    canvas.style.cssText = `
                position: fixed;
                left: 50%;
                top: 45%;
                transform: translateX(-50%);
                z-index: 999999;
                `
    gradual.addColorStop("0", color[0]);
    gradual.addColorStop("0.5", color[1]);
    ctx.strokeStyle = gradual;
    ctx.lineWidth = width;
    ctx.arc(size / 2, size / 2, size / 2 - width, 0, Math.PI * 2);
    ctx.stroke();
    canvas.className = 'loadingDom';
    this.query('body')[0].appendChild(canvas);
    const timeoutTimer = setTimeout(() => {
      if (this.query('.loadingDom').length > 0) {
        clearInterval(timer)
        this.query('body')[0].removeChild(this.query('.loadingDom')[0]);
        // this.showToast({
        //   text: '请求响应超时，请刷新'
        // })
      }
    }, timeout);
    const timer = setInterval(() => {
      rotate += 10;
      canvas.style.transform = `translateX(-50%) rotate(${rotate}deg)`;
      if (this.query('.loadingDom').length === 0) {
        clearInterval(timer);
        clearTimeout(timeoutTimer);
      }
    }, speed);
  },
  // 存储全局变量
  map: new Map(),
  // 设置session缓存
  store: {
    set (key, value) {
      localStorage.setItem(key, JSON.stringify(value));
    },
    get (key) {
      const val = localStorage.getItem(key);
      if (!val) return '';
      return JSON.parse(localStorage.getItem(key));
    },
    clear () {
      localStorage.clear();
    },
    remove (key) {
      localStorage.removeItem(key);
    },
    setS (key, value) {
      sessionStorage.setItem(key, JSON.stringify(value));
    },
    getS (key) {
      const val = sessionStorage.getItem(key)
      if (!val) return '';
      return JSON.parse(sessionStorage.getItem(key));
    },
    clearS () {
      sessionStorage.clear();
    },
    removeS (key) {
      sessionStorage.removeItem(key);
    }
  },
  // 获取随机字符串
  getRandStr(len) {
    len = len || 32;
    let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';   
    let maxPos = $chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
    　　pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  },


  // 通用HTTP请求方法
  http(params, url, method = 'post') {
    let self = this;

    message.loading('loading', 2.5);

    let config = {
      method  : method,
      url     : http + url,
      data    : params.data,
      headers : params.headers || {
        'Content-Type' : 'application/json;charset=UTF-8',
        'token'        : self.store.getS('token')
      }
    };

    if(method === 'get') {
      config.params = params.data;
      delete config.data; // GET请求不需要data
    }
    if(method === 'delete') {
      config.data = params.data; // DELETE请求需要data
    }
    if(method === 'put') {
      config.data = params.data; // PUT请求需要data
    }
    if(method === 'post') {
      config.data = params.data; // POST请求需要data
    }
    if(method === 'post' && params.headers && params.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
      // 如果是表单提交，需要转换data格式
      config.data = new URLSearchParams(params.data).toString();
    }
    if(method === 'post' && params.headers && params.headers['Content-Type'] === 'multipart/form-data') {
      // 如果是文件上传，需要转换data格式
      let formData = new FormData();
      for (let key in params.data) {
        formData.append(key, params.data[key]);
      }
      config.data = formData;
    }



    Axios(config).then(res => {
      if (!res || !res.data) return;

      message.destroy();

      let resData = res.data;
      let code    = resData.code;
      let msg     = resData.msg;


      if (code != "200") {
        message.error(msg);

        if (typeof params.fail === 'function') {
          params.fail(res);
        }

        return false;
      }

      if (typeof params.success === 'function') params.success(resData);

    }).catch(function (error) {
      if(!error.response) {
        return ;
      }
      message.destroy();

      let errorData = error.response.data;
      let msg       = errorData.msg || '请求失败，请稍后再试';
      let code      = errorData.code;

      message.error(msg);

      // 可以在这里处理token过期等全局错误
      if(msg == 'token错误') {
        self.store.clearS();
        // 跳转到登录页面需要在Vue组件中处理
        window.location.href = '#/login';
      }
    })
  },

  // POST请求的便捷方法
  post(params, url) {
    return this.http(params, url, 'post');
  },

  // GET请求的便捷方法
  get(params, url) {
    return this.http(params, url, 'get');
  },

  // PUT请求的便捷方法
  put(params, url) {
    return this.http(params, url, 'put');
  },

  // DELETE请求的便捷方法
  delete(params, url) {
    return this.http(params, url, 'delete');
  }

}

Vue.prototype.$utils = utils;

export default utils;
