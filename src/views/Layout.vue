<template>
  <div class="layout-container">
    <a-layout class="main-layout">
      <!-- 顶部导航 -->
      <topNav />
      
      <a-layout class="content-layout">
        <!-- 左侧导航 -->
        <leftSide :selected="selected" :open="open" />
        
        <!-- 主内容区域 -->
        <a-layout class="page-layout">
          <div class="page-content">
            <slot></slot>
          </div>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>

<script>
import topNav from "../components/topNav";
import leftSide from "../components/leftSide";

export default {
  name: 'Layout',
  components: {
    topNav,
    leftSide
  },
  props: {
    selected: {
      type: String,
      default: ''
    },
    open: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.layout-container {
  height: 100vh;
  overflow: hidden;
}

.main-layout {
  height: 100vh;
  background: #f5f5f5;
}

.content-layout {
  height: calc(100vh - 64px);
  background: transparent;
  margin-top: 64px; // 为固定的顶部导航留出空间
}

.page-layout {
  background: transparent;
  overflow-y: auto;
  height: 100%;
  margin-left: 200px; // 为固定的侧边栏留出空间
  padding: 0 24px 24px;
}

.page-content {
  min-height: 100%;
  width: 100%;
}

// 固定定位样式 - 使用更具体的选择器避免冲突
.layout-container /deep/ .ant-layout-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 100 !important;
  width: 100% !important;
}

.layout-container /deep/ .ant-layout-sider {
  position: fixed !important;
  left: 0 !important;
  top: 64px !important;
  height: calc(100vh - 64px) !important;
  z-index: 99 !important;
  overflow-y: auto !important;
}

// 确保布局容器不被全局样式影响
.layout-container .main-layout {
  background: #f5f5f5 !important;
}

.layout-container .content-layout {
  background: transparent !important;
}

.layout-container .page-layout {
  background: transparent !important;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-container /deep/ .ant-layout-header {
    position: relative !important;
    top: 0 !important;
  }

  .layout-container /deep/ .ant-layout-sider {
    position: relative !important;
    top: 0 !important;
    height: auto !important;
  }

  .content-layout {
    height: auto;
    margin-top: 0;
  }

  .page-layout {
    margin-left: 0;
  }

  .main-layout {
    height: auto;
    min-height: 100vh;
  }
}
</style> 