<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>
    
    <div class="login-content">
      <a-row>
        <a-col :span="8"></a-col>
        <a-col :span="8">
          <div class="login-card">
            <div class="login-header">
              <div class="brand-logo">
                <a-icon type="shield" />
              </div>
              <h2 class="login-title">后台登录</h2>
              <p class="login-subtitle">鑫澳斯盾管理系统</p>
            </div>
            
            <div class="login-form">
              <a-form :form="form">
                <a-form-item>
                  <a-input 
                    placeholder="请输入用户名" 
                    size="large"
                    class="custom-input"
                    v-decorator="['username',{ rules: [{ required: true, message: '请输入用户名' }] }]"
                  >
                    <a-icon slot="prefix" type="user" />
                  </a-input>
                </a-form-item>

                <a-form-item>
                  <a-input 
                    placeholder="请输入密码" 
                    size="large" 
                    type="password"
                    class="custom-input"
                    v-decorator="['password',{ rules: [{ required: true, message: '请输入密码' }] }]"
                  >
                    <a-icon slot="prefix" type="lock" />
                  </a-input>
                </a-form-item>

                <a-button 
                  type="primary" 
                  @click="check" 
                  block 
                  size="large"
                  class="login-button"
                  :loading="loginLoading"
                >
                  {{ loginLoading ? '登录中...' : '登录' }}
                </a-button>
              </a-form>
            </div>
          </div>
        </a-col>
        <a-col :span="8"></a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      username: '',
      password: '',
      loginLoading: false,
      form: this.$form.createForm(this, { name: 'coordinated' }),
    }
  },
  created: function () {
    
  },
  mounted: function () {
    this.$utils.store.getS('token');
  },

  methods: {
    check() {
      let self = this;
      self.loginLoading = true;
      
      // 模拟登录延迟
 
      
      self.form.validateFields((err, values) => {
        if (!err) {
           self.$utils.http({
            data:{
              username : values.username,
              password : values.password,
              login    : 1
            },
            headers:{

            },
            success:(res)=> {
              let data = res.data;
              self.$message.success(res.message);

              self.$utils.store.setS('token', data.unionid);

               self.$router.push({
                path: '/',
              })

            }

          }, '/admin/login')
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  }

  .bg-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;

      &.shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 150px;
        height: 150px;
        top: 70%;
        right: 10%;
        animation-delay: 2s;
      }

      &.shape-3 {
        width: 100px;
        height: 100px;
        top: 50%;
        left: 80%;
        animation-delay: 4s;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1200px;
  padding: 0 24px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: slideUp 0.8s ease-out;
  max-width: 400px;
  margin: 0 auto;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  padding: 40px 40px 20px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(64, 169, 255, 0.05) 100%);

  .brand-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
    animation: pulse 2s infinite;

    .anticon {
      color: white;
      font-size: 24px;
    }
  }

  .login-title {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
  }

  .login-subtitle {
    color: #8c8c8c;
    font-size: 14px;
    margin: 0;
    font-weight: 400;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(24, 144, 255, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
  }
}

.login-form {
  padding: 20px 40px 40px;

  .custom-input {
    border-radius: 12px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;

    &:hover {
      border-color: #d9d9d9;
      background: rgba(255, 255, 255, 0.9);
    }

    &:focus,
    &.ant-input-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      background: white;
    }

    /deep/ .ant-input {
      background: transparent;
      border: none;
      font-size: 14px;
      padding: 12px 16px 12px 40px;

      &::placeholder {
        color: #bfbfbf;
      }
    }

    /deep/ .ant-input-prefix {
      left: 16px;
      color: #8c8c8c;
      font-size: 16px;
    }
  }

  .login-button {
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border: none;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      transform: translateY(-2px);
      box-shadow: 0 12px 30px rgba(24, 144, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &.ant-btn-loading {
      background: linear-gradient(135deg, #91d5ff 0%, #bae7ff 100%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    padding: 0 16px;

    /deep/ .ant-col {
      &:first-child,
      &:last-child {
        display: none;
      }

      &:nth-child(2) {
        width: 100%;
        max-width: none;
      }
    }
  }

  .login-card {
    margin: 20px 0;
    border-radius: 16px;
  }

  .login-header {
    padding: 30px 24px 16px;

    .login-title {
      font-size: 20px;
    }
  }

  .login-form {
    padding: 16px 24px 30px;

    .custom-input {
      margin-bottom: 16px;
    }

    .login-button {
      height: 44px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .login-header {
    .brand-logo {
      width: 50px;
      height: 50px;
      margin-bottom: 16px;

      .anticon {
        font-size: 20px;
      }
    }

    .login-title {
      font-size: 18px;
    }

    .login-subtitle {
      font-size: 12px;
    }
  }
}
</style>