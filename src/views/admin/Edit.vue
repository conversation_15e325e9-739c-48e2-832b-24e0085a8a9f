<template>
  <Layout selected="admin" open="sub1">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="user" /> 管理员</a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="管理员"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
      >
        <a-form-model-item ref="name" label="用户名" prop="username">
          <a-input
            v-model="form.username"
            placeholder="用户名"
          />
        </a-form-model-item>
        <a-form-model-item ref="password" label="密码" prop="password">
          <a-input
            v-model="form.password"
            placeholder="密码"
            type="password"
          />
        </a-form-model-item>
       
        <a-row type="flex" justify="start">
          <a-col :span="12">
            <a-button type="primary" @click="onSubmit" block>
              <a-icon type="check-circle" />提交
            </a-button>
          </a-col>
          <a-col :span="12">
            <a-button style="margin-left: 10px;" @click="resetForm" block>
              <a-icon type="undo" />重置
            </a-button>
          </a-col>
        </a-row>
          
          
      
      </a-form-model>
    </a-layout-content>
  </Layout>
</template>
<script>
import Layout from "../Layout";
import leftSide from "../../components/leftSide";
import atable   from "../../components/atable";
export default {
  components: { Layout, leftSide , atable},
  data() {
    return {
      //表单
      form: {
        username: '',
        password: '',
      }, 
      //规则
       rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '最小长度 3 - 20 位', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 4, max: 20, message: '最小长度 4 - 20 位', trigger: 'blur' },
        ],
      },

      id : 0
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.username = editData.username;
       

    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        if (!valid) {
          return false;
        }

        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;
        let data = {
              username : self.form.username,
              password : self.form.password
        }
        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data:data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.push({"path":"/"})

            }

          }, '/admin/admin/edit')
      
    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
