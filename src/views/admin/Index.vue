<template>
  <Layout selected="admin" open="sub1">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="user" /> 管理员</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="管理员列表"
    >
      <template slot="extra">
        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:'/adminEdit'})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable
        :tabSourceData="tabSourceData"
        :tabColumns="tabColumns"
        :apiDel="apiDel"
        :buttonEditUrl='buttonEditUrl'
        :total="totalCount"
        @list="list"
        @pageChange="handlePageChange"
      ></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";

export default {
  name: 'AdminIndex',
  components: {
    Layout,
    atable
  },
  data() {
    return {
      tabSourceData: [],
      tabColumns: [
        {
          title: 'id',
          dataIndex: 'id',
          key: 'id',
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
        },
        {
          title: '创建时间',
          dataIndex: 'create_time',
          key: 'create_time',
          scopedSlots: { customRender: 'create_time_label' },
        },
        {
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
        },
      ],
      apiDel: '/admin/del',
      buttonEditUrl: '/adminEdit',

      // 总记录数
      totalCount: 0
    }
  },
  created() {
    this.list();
  },
  methods: {
    list(page = 1, pageSize = 10) {
      let self = this;
      self.$utils.get({
        data: {
          page: page,
          limit: pageSize
        },
        success: (res) => {
          // 兼容不同的API响应格式
          let data = res.data.list || res.data || [];
          let total = res.data.total || res.total || 0;

          // 如果没有明确的total，使用数据长度作为估算
          if (total === 0 && data.length > 0) {
            // 如果当前页有数据且是满页，估算总数
            if (data.length === pageSize) {
              total = page * pageSize + 1; // 至少还有下一页
            } else {
              total = (page - 1) * pageSize + data.length;
            }
          }

          // 更新总记录数
          self.totalCount = total;

        

          self.tabSourceData = data;
        },
        fail: (err) => {
          console.error('获取管理员列表失败:', err);
        }
      }, '/admin/list')
    },

    // 处理分页变化
    handlePageChange(paginationInfo) {
      this.list(paginationInfo.current, paginationInfo.pageSize);
    }
  }
}
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
