<template>
  <Layout selected="advantageDetails" open="sub4">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="compass" /> 产品优势</a-breadcrumb-item>
      <a-breadcrumb-item>详情</a-breadcrumb-item>
      <a-breadcrumb-item>列表</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品优势 详情"
      sub-title="列表"
    >
      <template slot="extra">
        <a-dropdown key="2">
          <a-menu slot="overlay" @click="onClick">
            <a-menu-item :key="0">全部</a-menu-item>
            <a-menu-item v-for="(item, index) in sortListData" :key="item.id">{{item.advantage_title}}</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> {{buttonText}} <a-icon type="down" /> </a-button>
        </a-dropdown>

        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable :tabSourceData="tabSourceData" :tabColumns="tabColumns" :apiDel="apiDel" :buttonEditUrl='buttonEditUrl' @list="list"></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '标题',
            dataIndex: 'advantage_title',
            key: 'advantage_title'
          },
         {
            title: '详情标题',
            dataIndex: 'detail_title',
            key: 'detail_title'
          },
         {
            title: '描述',
            dataIndex: 'detail_content',
            key: 'detail_content',
            scopedSlots: { customRender: 'toolTip' }
          },
           {
            title: 'banner',
            dataIndex: 'detail_banner',
            key: 'detail_banner',
            scopedSlots: { customRender: 'imgAlert' }
          },
           {
            title: '封面',
            dataIndex: 'detail_cover',
            key: 'detail_cover',
            scopedSlots: { customRender: 'imgAlert' }
          },
          {
            title: '详情内容',
            dataIndex: 'detail_info',
            key: 'detail_info',
            scopedSlots: { customRender: 'infoImgUploadType2' }
          },
           {
            title: '位置',
            dataIndex: 'detail_cover_position',
            key: 'detail_cover_position',
            scopedSlots: { customRender: 'tag' }
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            scopedSlots: { customRender: 'create_time_label' }
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/api/common/dataDel",

      //按钮编辑前往地址
      buttonEditUrl:"/advantage/detailEdit",
      sortListData : [],
      buttonText   : '筛选',
      oldTabSourceData :[]
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'advantageDetail');
  },
  mounted:function () {
    this.$utils.store.getS('token');

    this.sortList();
  },

  methods: {
    list() {
        let self = this;
        self.$utils.http({
            data:{
              //page  : 1,
              //table : 'configIndexBanner' 
            },
            success:(res)=> {
              let data = res.data;
              
              data.forEach(element => {
                element.key = element.id,
                element.create_time = self.$utils.getDate(element.create_time);
              });
              
              self.tabSourceData = data;

            }

          }, '/api/advantage/detailPageList')
      
    },
    sortList() {
        let self = this;
        self.$utils.http({
            data:{
             
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/advantage/pageList')
      
    },

    onClick({ key  }) {
      let self          = this;
      let tabSourceData = self.tabSourceData;
      let newSourceData = [];
      let sortListData  = self.sortListData;
      if(key == 0) {
        self.buttonText = '筛选'
        self.list();
        return ;
      }

      if(self.oldTabSourceData.length == 0) {
        self.oldTabSourceData = tabSourceData;
      }


      let oldTabSourceData = self.oldTabSourceData;

      oldTabSourceData.forEach(element => {
          element.key         = element.id,
          element.create_time = self.$utils.getDate(element.create_time);
          if(key  == Number(element.advantage_id)) {
            newSourceData.push(element);
          }
      });
      
      self.tabSourceData = newSourceData;

      sortListData.forEach(element => {
          if(key  == Number(element.id)) {
            self.buttonText = element.advantage_title;
          }
      });

    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
