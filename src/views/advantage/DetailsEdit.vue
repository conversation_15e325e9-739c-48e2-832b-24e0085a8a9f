<template>
  <Layout selected="advantageDetails" open="sub4">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/advantage/details"><a-icon type="trophy" /> 产品优势</a></a-breadcrumb-item>
      <a-breadcrumb-item><a href="/#/advantage/details">详情</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品优势详情"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
             <a-form-model-item label="产品优势" prop="advantageId">
              <a-select v-model="form.advantageId" placeholder="请选择所属产品优势">
                <a-select-option value="">
                请选择所属产品优势
                </a-select-option>
                <a-select-option :value="item.id" v-for="item in sortListData" :key="item.id" @click="selectChange(item.advantage_title)">
                {{item.advantage_title}}
                </a-select-option>
              </a-select>
          
            </a-form-model-item>

            <a-form-model-item label="详细标题" prop="detailTitle">
              <a-input
                v-model="form.detailTitle"
                placeholder="详细标题"
              />
            </a-form-model-item>

            <a-form-model-item label="详情描述" prop="detailContent" >
              <a-textarea
                v-model="form.detailContent"
                placeholder="详情描述"
                :rows="4"
              />
            </a-form-model-item>

            <a-form-model-item label="banner" prop="detailBanner">
             <aupload func="getUploadDetailBanner" @getUploadDetailBanner="getUploadDetailBanner($event)" :sourceValue="form.detailBanner"></aupload>
            </a-form-model-item>

            
            <a-form-model-item label="封面" prop="advantageCover">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.detailCover"></aupload>
            </a-form-model-item>
            
            <a-form-model-item label="封面位置" prop="detailCoverPosition">
              <a-radio-group v-model="form.detailCoverPosition" >
                <a-radio :value="1">
                 左
                </a-radio>
                <a-radio :value="2">
                 右
                </a-radio>
              </a-radio-group>
            </a-form-model-item>


            <a-form-model-item label="内容" prop="detailInfo">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.detailInfo"></editor>
            </a-form-model-item>



            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
    </Layout>
  </Layout>
</template>
<script>
import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  name: 'AdvantageDetailsEdit',
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        advantageId         : '',
        advantageTitle      : '',
        detailTitle         : '',
        detailContent       : '',
        detailBanner        : '',
        detailCover         : '',
        detailInfo          : '',
        detailCoverPosition : 1,
        
      }, 
      //规则
       rules: {
        advantageId: [
          { required: true, message: '请选择产品优势', trigger: 'blur' }
        ],
        detailTitle: [
          { required: true, message: '请输入详情标题', trigger: 'blur' },
        ],
        detailContent: [
          { required: true, message: '请输入描述', trigger: 'blur' },
        ],
        detailBanner: [
          { required: true, message: '请上传banner', trigger: 'blur' },
        ],
        detailCover: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
        detailCoverPosition: [
          { required: true, message: '请选择位置', trigger: 'blur' },
        ],
        detailInfo: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[]

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.advantageId         = editData.advantage_id;
       self.form.advantageTitle      = editData.advantage_title;
       self.form.detailTitle         = editData.detail_title;
       self.form.detailContent       = editData.detail_content;
       self.form.detailBanner        = editData.detail_banner;
       self.form.detailCover         = editData.detail_cover;
       self.form.detailCoverPosition = editData.detail_cover_position;
       self.form.detailInfo          = editData.detail_info;
       

    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/advantage/detailEdit')
      
    },

     sortList() {
        let self = this;
        self.$utils.http({
            data:{
             
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/advantage/pageList')
      
    },

     //接受富文本值
    editorVal(html) {
      this.form.detailInfo = html;
    },

    //接受 值
    selectChange(val) {

      this.form.advantageTitle = val;
    },


    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.detailCover = val;
    },

    //接受示例图值
    getUploadDetailBanner(val) {

      //console.log(val);
       this.form.detailBanner = val;
    },


  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
