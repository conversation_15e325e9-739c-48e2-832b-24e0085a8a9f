<template>
  <Layout selected="advantage" open="sub4">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/advantage/index"><a-icon type="trophy" /> 产品优势</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品优势"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >

            <a-form-model-item label="标题" prop="advantageTitle">
              <a-input
                v-model="form.advantageTitle"
                placeholder="标题"
              />
            </a-form-model-item>
            <a-form-model-item label="小标题" prop="advantageTitleSecond">
              <a-input
                v-model="form.advantageTitleSecond"
                placeholder="小标题"
              />
            </a-form-model-item>
            <a-form-model-item label="简介" prop="advantageSummary" >
              <a-textarea
                v-model="form.advantageSummary"
                placeholder="简介"
                :rows="4"
              />
            </a-form-model-item>

            <a-form-model-item label="封面" prop="advantageCover">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.advantageCover"></aupload>
            </a-form-model-item>

            <a-form-model-item label="排序" prop="advantageSort" >
              <a-input-number v-model="form.advantageSort" :min="1" :max="999" placeholder="排序" style="width:200px;"/>
            </a-form-model-item>


            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
</template>
<script>
import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  name: 'AdvantageListEdit',
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        advantageTitle       : '',
        advantageTitleSecond : '',
        advantageSummary     : '',
        advantageCover       : '',
        advantageSort        : 1,
        
      }, 
      //规则
       rules: {
        advantageTitle: [
          { required: true, message: '请选择标题', trigger: 'blur' }
        ],
        advantageTitleSecond: [
          { required: true, message: '请输入小标题', trigger: 'blur' },
        ],
        advantageSummary: [
          { required: true, message: '请输入简介', trigger: 'blur' },
        ],
        advantageCover: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
        advantageSort: [
          { required: true, message: '请输入排序', trigger: 'blur' },
        ],
      },
      id : 0

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.advantageTitle       = editData.advantage_title;
       self.form.advantageTitleSecond = editData.advantage_title_second;
       self.form.advantageSummary     = editData.advantage_summary;
       self.form.advantageCover       = editData.advantage_cover;
       self.form.advantageSort        = editData.advantage_sort;
       

    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/advantage/edit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'albumSort'
            },
            success:(res)=> {
              let data          = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList') 
    },

    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.advantageCover = val;
    },


  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
