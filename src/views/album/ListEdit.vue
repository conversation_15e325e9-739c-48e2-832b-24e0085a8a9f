<template>
  <Layout selected="album" open="sub3">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/album/index"><a-icon type="picture" /> 相册</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="相册"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
      >
        <a-form-model-item label="分类" prop="albumSortId">
          <a-radio-group v-model="form.albumSortId" >
            <a-radio :value="item.id" v-for="item in sortListData" :key="item.id" @focus="radioChange(item.album_sort_name)">
              {{item.album_sort_name}}
            </a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="标题" prop="albumTitle">
          <a-input
            v-model="form.albumTitle"
            placeholder="标题"
          />
        </a-form-model-item>
        <a-form-model-item label="简介" prop="albumSummary" >
          <a-textarea
            v-model="form.albumSummary"
            placeholder="简介"
            :rows="4"
          />
        </a-form-model-item>

        <a-form-model-item label="封面" prop="albumCover">
         <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.albumCover"></aupload>
           <a-input
            v-model="form.albumCover"
            type="hidden"
          />
        </a-form-model-item>
   
        <a-row type="flex" justify="start">
          <a-col :span="12">
          <a-button type="primary" @click="onSubmit" block>
                <a-icon type="check-circle" />提交
            </a-button>
          </a-col>
          <a-col :span="12">
            <a-button style="margin-left: 10px;" @click="resetForm" block>
                <a-icon type="undo" />重置
            </a-button>
          </a-col>
        </a-row>
          
          
      
      </a-form-model>
    </a-layout-content>
  </Layout>
</template>
<script>
import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";


export default {
  name: 'AlbumListEdit',
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        albumSortId   : '',
        albumSortName : '',
        albumTitle    : '',
        albumSummary  : '',
        albumCover    : ''
        
      }, 
      //规则
       rules: {
        albumSortId: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        albumTitle: [
          { required: true, message: '请输入标题', trigger: 'blur' },
        ],
        albumSummary: [
          { required: true, message: '请输入简介', trigger: 'blur' },
        ],
        albumCover: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
      },

      id : 0,

      sortListData:[],

      infoSize:[
        {}
      ],


    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.albumSortId   = editData.album_sort_id;
       self.form.albumSortName = editData.album_sort_name;
       self.form.albumTitle    = editData.album_title;
       self.form.albumSummary  = editData.album_summary;
       self.form.albumCover    = editData.album_cover;
       

    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/album/edit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'albumSort'
            },
            success:(res)=> {
              let data          = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList') 
    },

    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.albumCover = val;
    },

    //接受 值
    radioChange(val) {
      this.form.albumSortName = val;
    },
    //接受 值
    getUploadInfo(val) {
    
    val.forEach((element, index) => {
      this.form.albumInfo[index] = element;
    });
 
    
       
    },

    //添加上传图片
    addInfo() {
       this.form.albumInfo.push({small:'', big:''});
    },
    //添加上传图片
    removeInfo(index) {


       console.log(index);

       delete(this.form.albumInfo[index]);
  
       let newArray      = [];
       let newArrayIndex = 0;

      this.form.albumInfo.forEach((element, i) => {


        newArray[newArrayIndex] = element;
        newArrayIndex++;
      });

       this.form.albumInfo = newArray;
      console.log(this.form.albumInfo)


    }



  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
