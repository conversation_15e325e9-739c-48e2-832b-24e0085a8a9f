<template>
  <Layout selected="albumSort" open="sub3">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/album/sort"><a-icon type="picture" /> 相册</a></a-breadcrumb-item>
      <a-breadcrumb-item><a href="/#/album/sort">分类</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="分类"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
      >
        <a-form-model-item ref="name" label="名称" prop="albumSortName">
              <a-input
                v-model="form.albumSortName"
                placeholder="名称"
              />
            </a-form-model-item>
           
             <!-- <a-form-model-item label="banner图片" prop="albumBanner">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.albumBanner"></aupload>
               <a-input
                v-model="form.albumBanner"
                type="hidden"
              />
            </a-form-model-item> -->

            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
    </a-layout>
  </a-layout>
</template>
<script>
import Layout from "../Layout";

import aupload   from "../../components/aupload";
export default {
  name: 'AlbumSortEdit',
  components: { Layout, aupload},
  data() {
    return {
      //表单
      form: {
        albumBanner    : '',
        albumSortName  : ''
      }, 
      //规则
       rules: {
        albumSortName: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 20, message: '最小长度 2 - 20 位', trigger: 'blur' },
        ],
        // albumBanner: [
        //   { required: true, message: '请上传图片', trigger: 'blur' }
        // ]
      },

      id : 0
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

      let editData = self.$utils.store.get('editData');
       
       self.form.albumBanner   = editData.album_banner;
       self.form.albumSortName = editData.album_sort_name;
       

    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        if (!valid) {
          return false;
        }

        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

        let data   = self.form;
        //data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/album/sortEdit')
      
    },
    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.albumBanner = val;
    },
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
