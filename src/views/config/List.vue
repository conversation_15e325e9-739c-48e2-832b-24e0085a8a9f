<template>
  <Layout selected="configList" open="sub2">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="tool" /> 配置</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="配置"
      sub-title="列表"
    >
      <template slot="extra">
        <!-- <a-dropdown key="2">
          <a-menu slot="overlay" @click="onClick">
            <a-menu-item :key="0">全部</a-menu-item>
            <a-menu-item v-for="(item, index) in sortListData" :key="item.id">{{item.config_sort}}</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> {{buttonText}} <a-icon type="down" /> </a-button>
        </a-dropdown> -->
        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable 
      :tabSourceData="tabSourceData" 
      :tabColumns="tabColumns" 
      :apiDel="apiDel"
      :apiDelMethod="apiDelMethod"
      :buttonEditUrl='buttonEditUrl' 
      :total="totalCount" 
      @pageChange="handlePageChange">
    </atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData    : [],
      totalCount:0,
      oldTabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '配置名',
            dataIndex: 'config_name',
            key: 'config_name'
          },
         {
            title: '配置值',
            dataIndex: 'config_value',
            key: 'config_value',
            scopedSlots: { customRender: 'configInfoAlert' }
          },
          {
            title: '描述',
            dataIndex: 'desc',
            key: 'desc'
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time'
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/admin/config/del",
      apiDelMethod:'get',

      //按钮编辑前往地址
      buttonEditUrl:"/ConfigListEdit",

      sideSelect   : 2,
      sortId       : 0,
      sortListData : [],
      buttonText   : '筛选'
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'configCommon');
  },
  mounted:function () {
    this.$utils.store.getS('token');
  },

  methods: {
    list(page = 1, pageSize = 10) {
        let self = this;
        self.$utils.get({
            data:{
              page  : page,
              limit : pageSize
            },
            success:(res)=> {
              let data = res.data.list;
              let total = res.data.total || res.total || 0;

              // 更新总记录数
              self.totalCount = total;


              data.forEach(element => {
                element.key = element.id;
              });
              
              self.tabSourceData = data;

            }

          }, '/admin/config/list')
    },
     // 处理分页变化
    handlePageChange(paginationInfo) {
      console.log('分页变化:', paginationInfo);
      this.list(paginationInfo.current, paginationInfo.pageSize);
    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
