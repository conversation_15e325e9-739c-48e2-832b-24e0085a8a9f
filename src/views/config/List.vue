<template>
  <Layout selected="configList" open="sub2">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="tool" /> 配置</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="配置"
      sub-title="列表"
    >
      <template slot="extra">
        <a-dropdown key="2">
          <a-menu slot="overlay" @click="onClick">
            <a-menu-item :key="0">全部</a-menu-item>
            <a-menu-item v-for="(item, index) in sortListData" :key="item.id">{{item.config_sort}}</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> {{buttonText}} <a-icon type="down" /> </a-button>
        </a-dropdown>
        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable :tabSourceData="tabSourceData" :tabColumns="tabColumns" :apiDel="apiDel" :buttonEditUrl='buttonEditUrl'></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData    : [],
      oldTabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '分类',
            dataIndex: 'config_common_sort_name',
            key: 'config_common_sort_name'
          },
         {
            title: '配置名',
            dataIndex: 'config_name',
            key: 'config_name'
          },
         {
            title: '详情',
            dataIndex: 'config_key',
            key: 'config_key',
            scopedSlots: { customRender: 'configInfoAlert' }
          },
         {
            title: '示意图',
            dataIndex: 'config_explan_img',
            key: 'config_explan_img',
            scopedSlots: { customRender: 'imgAlert' }
          },
         {
            title: '类型',
            dataIndex: 'config_type',
            key: 'config_type',
            scopedSlots: { customRender: 'configTypeLabel' }
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            scopedSlots: { customRender: 'create_time_label' }
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/api/common/dataDel",

      //按钮编辑前往地址
      buttonEditUrl:"/ConfigListEdit",

      sideSelect   : 2,
      sortId       : 0,
      sortListData : [],
      buttonText   : '筛选'
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'configCommon');

    this.sortList();
  },
  mounted:function () {
    this.$utils.store.getS('token');
  },

  methods: {
    list() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'configCommon' 
            },
            success:(res)=> {
              let data = res.data;
              
              data.forEach(element => {
                element.key         = element.id,
                element.create_time = self.$utils.getDate(element.create_time);
              });
              
              self.tabSourceData = data;

            }

          }, '/api/common/pageList')
    },
     sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'configCommonSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },
     onClick({ key  }) {
      let self          = this;
      let tabSourceData = self.tabSourceData;
      let newSourceData = [];
      let sortListData  = self.sortListData;
      if(key == 0) {
        self.buttonText = '筛选'
        self.list();
        return ;
      }

      if(self.oldTabSourceData.length == 0) {
        self.oldTabSourceData = tabSourceData;
      }


      let oldTabSourceData = self.oldTabSourceData;

      oldTabSourceData.forEach(element => {
          element.key         = element.id,
          element.create_time = self.$utils.getDate(element.create_time);
          if(key  == Number(element.config_common_sort_id)) {
            newSourceData.push(element);
          }
      });
      
      self.tabSourceData = newSourceData;

      sortListData.forEach(element => {
          if(key  == Number(element.id)) {
            self.buttonText = element.config_sort;
          }
      });

    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
