<template>
  <a-layout id="components-layout-demo-top-side-2" style="min-height:100%;">
    <topNav > </topNav>
    <a-layout>
      <leftSide selected="configList"  open="sub2"></leftSide>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item > <a href="/#/ConfigList"><a-icon type="tool" /> 配置</a></a-breadcrumb-item>
          <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
        </a-breadcrumb>

         <a-page-header
            style="border: 1px solid rgb(235, 237, 240);background:#fff;"
            title="配置"
            sub-title="编辑"
            @back="() => $router.go(-1)"
          />
          <br />

        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
        >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >

        

           <a-form-model-item label="分类" prop="config_common_sort_id">
              <!-- <a-radio-group v-model="form.config_common_sort_id" >
                <a-radio :value="item.id" v-for="item in sortListData" :key="item.id">
                  {{item.config_sort}}
                </a-radio>
              </a-radio-group> -->
                <a-select
            show-search
            placeholder="Select a person"
            option-filter-prop="children"
            :filter-option="filterOption"
             v-model="form.config_common_sort_id"

              @change="radioChange"
          >
            <a-select-option :value="item.id" v-for="item in sortListData" :key="item.id" >
              {{item.config_sort}}
            </a-select-option>
          </a-select>

            </a-form-model-item>
           <a-form-model-item label="类型" prop="config_type">
              <a-radio-group v-model="form.config_type">
                <a-radio :value="1">
                  文字
                </a-radio>
                <a-radio :value="2">
                  图片
                </a-radio>
                <a-radio :value="3">
                  富文本
                </a-radio>
              </a-radio-group>
            </a-form-model-item>

            <a-form-model-item label="名称" prop="config_name">
              <a-input
                v-model="form.config_name"
                placeholder="配置名称"
              />
            </a-form-model-item>
            <a-form-model-item label="键" prop="config_key" >
              <a-input
                v-model="form.config_key"
                placeholder="配置键"
              />
            </a-form-model-item>
           
            <a-form-model-item label="值" prop="config_value" v-if="form.config_type == 1">
              <a-input
                v-model="form.config_value"
                placeholder="配置值"
              />
            </a-form-model-item>

            <a-form-model-item label="值" prop="config_value" v-if="form.config_type == 2">
             <aupload func="getUploadConfigValue" @getUploadConfigValue="getUploadConfigValue($event)" :sourceValue="form.config_value"></aupload>
              <a-input
                v-model="form.config_value"
                type="hidden"
              />
            </a-form-model-item>


            <a-form-model-item label="值" prop="config_value" v-if="form.config_type == 3">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.config_value"></editor>
            </a-form-model-item>



            <a-form-model-item label="示例图" prop="config_explan_img">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.config_explan_img"></aupload>
               <a-input
                v-model="form.config_explan_img"
                type="hidden"
              />
            </a-form-model-item>
           
            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";


export default {
  components: { topNav, leftSide , editor, aupload},
  data() {
    return {
      //表单
      form: {
        config_common_sort_id   : '',
        config_common_sort_name : '',
        config_type             : 1,
        config_name             : '',
        config_key              : '',
        config_value            : '',
        config_explan_img       : '',
        
      }, 
      //规则
       rules: {
        config_common_sort_id: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        config_type: [
          { required: true, message: '请选择类型', trigger: 'blur' },
        ],
        config_name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        config_key: [
          { required: true, message: '请输入键', trigger: 'blur' },
        ],
        config_value: [
          { required: true, message: '请输入值', trigger: 'blur' },
        ],
        config_explan_img: [
          // { required: true, message: '请上传示例图', trigger: 'blur' },
        ]
      },

      id : 0,

      sortListData:[],


    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;

       if(editData.config_type == 2) {
         self.form.config_value = editData.config_value;
       }

       self.form.config_explan_img = editData.config_explan_img;
       

    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'configCommonSort' 
            },
            success:(res)=> {
              let data          = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList') 
    },

    //接受富文本值
    editorVal(html) {
      this.form.config_value = html;
    },

    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.config_explan_img = val;
    },
    //接受 值
    getUploadConfigValue(val) {
       this.form.config_value = val;
       //console.log(val);
    },
    //接受 值
    radioChange(val) {

      console.log(val);
      this.sortListData.forEach((v, i)=> {
        if (v.id == val) {
          this.form.config_common_sort_name = v.config_sort;
        };
      })
      
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
