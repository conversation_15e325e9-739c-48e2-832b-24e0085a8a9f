<template>
  <a-layout id="components-layout-demo-top-side-2" style="min-height:100%;">
    <topNav > </topNav>
    <a-layout>
      <leftSide selected="configList"  open="sub2"></leftSide>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item > <a href="/#/ConfigList"><a-icon type="tool" /> 配置</a></a-breadcrumb-item>
          <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
        </a-breadcrumb>

         <a-page-header
            style="border: 1px solid rgb(235, 237, 240);background:#fff;"
            title="配置"
            sub-title="编辑"
            @back="() => $router.go(-1)"
          />
          <br />

        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
        >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >

            <a-form-model-item label="配置key" prop="config_name">
              <a-input
                v-model="form.config_name"
                placeholder="配置key"
              />
            </a-form-model-item>

            <a-form-model-item label="配置值" prop="config_value">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.config_value"></editor>
            </a-form-model-item>

            <a-form-model-item label="描述" prop="desc">
              <a-input
                v-model="form.desc"
                placeholder="描述"
              />
            </a-form-model-item>


            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";


export default {
  components: { topNav, leftSide , editor, aupload},
  data() {
    return {
      //表单
      form: {
     
        config_name  : '',
        config_value            : '',
        desc                    : ''
        
      }, 
      //规则
       rules: {
        config_name: [
          { required: true, message: '请输入key', trigger: 'blur' },
        ],
        config_value: [
          { required: true, message: '请输入值', trigger: 'blur' },
        ],
      },

      id : 0,
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;

    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

              self.$router.go('-1');

            }

          }, '/admin/config/edit')
      
    },

    //接受富文本值
    editorVal(html) {
      this.form.config_value = html;
    },

    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.config_explan_img = val;
    },
    //接受 值
    getUploadConfigValue(val) {
       this.form.config_value = val;
       //console.log(val);
    },
    //接受 值
    radioChange(val) {

      console.log(val);
      this.sortListData.forEach((v, i)=> {
        if (v.id == val) {
          this.form.config_common_sort_name = v.config_sort;
        };
      })
      
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
