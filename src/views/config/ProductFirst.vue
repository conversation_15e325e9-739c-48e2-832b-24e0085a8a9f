<template>
  <Layout selected="configProductFirst" open="sub2">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="tool" /> 配置</a-breadcrumb-item>
      <a-breadcrumb-item>首页产品 第一版</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="首页产品 第一版"
      sub-title="列表"
    >
      <template slot="extra">
        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable :tabSourceData="tabSourceData" :tabColumns="tabColumns" :apiDel="apiDel" :buttonEditUrl='buttonEditUrl' @list="list"></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '产品名称',
            dataIndex: 'product_name',
            key: 'product_name'
          },
         {
            title: '标题第一行',
            dataIndex: 'index_title_first',
            key: 'index_title_first'
          },
         {
            title: '标题第二行',
            dataIndex: 'index_title_second',
            key: 'index_title_second'
          },
         {
            title: '标题第三行',
            dataIndex: 'index_title_third',
            key: 'index_title_third'
          },
            {
            title: '产品详情',
            dataIndex: 'index_product_details',
            key: 'index_product_details',
            scopedSlots: { customRender: 'htmlInfoAlert' }
          },
            {
            title: '产品性能',
            dataIndex: 'index_performance',
            key: 'index_performance',
            scopedSlots: { customRender: 'htmlInfoAlert' }
          },
           {
            title: '左侧图',
            dataIndex: 'index_left_img',
            key: 'index_left_img',
            scopedSlots: { customRender: 'imgAlert' }
          },
           {
            title: '右侧图',
            dataIndex: 'index_right_img',
            key: 'index_right_img',
            scopedSlots: { customRender: 'imgAlert' }
          },
           {
            title: '排序',
            dataIndex: 'index_sort',
            key: 'index_sort',
            scopedSlots: { customRender: 'dataSort' }
          },
           {
            title: '显示',
            dataIndex: 'index_status',
            key: 'index_status',
            scopedSlots: { customRender: 'openClose' }
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            scopedSlots: { customRender: 'create_time_label' }
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/api/common/dataDel",

      //按钮编辑前往地址
      buttonEditUrl:"/config/productFirst/edit",
      sortListData : [],
      buttonText   : '筛选',
      oldTabSourceData :[]
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'configIndexProduceSite');
  },
  mounted:function () {
    this.$utils.store.getS('token');

    //this.sortList();
  },

  methods: {
     list() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'configIndexProduceSite' 
            },
            success:(res)=> {
              let data = res.data;
              
              data.forEach(element => {
                element.key = element.id,
                element.create_time = self.$utils.getDate(element.create_time);
              });
              
              self.tabSourceData = data;

            }

          }, '/api/common/pageList')
      
    },
    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'productSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

    onClick({ key  }) {
      let self          = this;
      let tabSourceData = self.tabSourceData;
      let newSourceData = [];
      let sortListData  = self.sortListData;
      if(key == 0) {
        self.buttonText = '筛选'
        self.list();
        return ;
      }

      if(self.oldTabSourceData.length == 0) {
        self.oldTabSourceData = tabSourceData;
      }


      let oldTabSourceData = self.oldTabSourceData;

      oldTabSourceData.forEach(element => {
          element.key         = element.id,
          element.create_time = self.$utils.getDate(element.create_time);
          if(key  == Number(element.product_sort_id)) {
            newSourceData.push(element);
          }
      });
      
      self.tabSourceData = newSourceData;

      sortListData.forEach(element => {
          if(key  == Number(element.id)) {
            self.buttonText = element.product_sort_name;
          }
      });

    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
