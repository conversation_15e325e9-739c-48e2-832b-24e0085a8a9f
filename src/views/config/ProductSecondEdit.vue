<template>
  <a-layout id="components-layout-demo-top-side-2" style="min-height:100%;">
    <topNav > </topNav>
    <a-layout>
      <leftSide selected="configProductSecond"  open="sub2"></leftSide>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
           <a-breadcrumb-item><a-icon type="tool" /> 配置</a-breadcrumb-item>
          <a-breadcrumb-item>首页产品 第二版</a-breadcrumb-item>
          <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
        </a-breadcrumb>

         <a-page-header
            style="border: 1px solid rgb(235, 237, 240);background:#fff;"
            title="首页产品 第二版"
            sub-title="编辑"
            @back="() => $router.go(-1)"
          />
          <br />

        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
        >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
             <a-form-model-item label="产品" prop="product_id">
              <a-select v-model="form.product_id" placeholder="请选择产品">
                <a-select-option value="">
                请选择产品
                </a-select-option>
                <a-select-option :value="item.id" v-for="item in sortListData" :key="item.id" @click="selectChange(item.product_name)">
                {{item.product_name}}
                </a-select-option>
              </a-select>
          
            </a-form-model-item>

            <a-form-model-item label="右侧标题第一行" prop="index_right_title_first">
              <a-input
                v-model="form.index_right_title_first"
                placeholder="右侧标题第一行"
              />
            </a-form-model-item>
            <a-form-model-item label="右侧标题第二行" prop="index_right_title_second">
              <a-input
                v-model="form.index_right_title_second"
                placeholder="右侧标题第二行"
              />
            </a-form-model-item>
            <a-form-model-item label="右侧标题第三行" prop="index_right_title_third">
              <a-input
                v-model="form.index_right_title_third"
                placeholder="右侧标题第三行"
              />
            </a-form-model-item>
 
            <a-form-model-item label="左侧标题第一行" prop="index_left_title_first">
              <a-input
                v-model="form.index_left_title_first"
                placeholder="左侧标题第一行"
              />
            </a-form-model-item>
            <a-form-model-item label="左侧标题第二行" prop="index_left_title_second">
              <a-input
                v-model="form.index_left_title_second"
                placeholder="左侧标题第二行"
              />
            </a-form-model-item>
            <a-form-model-item label="左侧标题第三行" prop="index_left_title_third">
              <a-input
                v-model="form.index_left_title_third"
                placeholder="左侧标题第三行"
              />
            </a-form-model-item>
 


            <a-form-model-item label="左侧图" prop="index_left_img">
             <aupload func="getUploadColorImg" @getUploadColorImg="getUploadColorImg($event)" :sourceValue="form.index_left_img"></aupload>
            </a-form-model-item>


            <a-form-model-item label="右侧图" prop="index_right_img">
             <aupload func="getUploadImg" @getUploadImg="getUploadImg($event)" :sourceValue="form.index_right_img"></aupload>
            </a-form-model-item>
         

            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  components: { topNav, leftSide , editor, aupload},
  data() {
    return {
      //表单
      form: {
        product_id               : '',
        product_name             : '',
        index_right_title_first  : '',
        index_right_title_second : '',
        index_right_title_third  : '',
        index_left_title_first   : '',
        index_left_title_second  : '',
        index_left_title_third   : '',
        index_left_img           : '',
        index_right_img          : ''
        
      }, 
      //规则
       rules: {
        product_id: [
          { required: true, message: '请选择产品', trigger: 'blur' }
        ],
        index_right_title_first: [
          { required: true, message: '请输入右侧标题第一行', trigger: 'blur' }
        ],
        index_right_title_second: [
          { required: true, message: '请输入右侧标题第二行', trigger: 'blur' },
        ],
        index_right_title_third: [
          { required: true, message: '请输入右侧标题第三行', trigger: 'blur' },
        ],
        index_left_title_first: [
          { required: true, message: '请输入左侧标题第一行', trigger: 'blur' }
        ],
        index_left_title_second: [
          { required: true, message: '请输入左侧标题第二行', trigger: 'blur' },
        ],
        index_left_title_third: [
          { required: true, message: '请输入左侧标题第三行', trigger: 'blur' },
        ],
       
        index_left_img: [
          { required: true, message: '请上传左侧图', trigger: 'blur' },
        ],
        index_right_img: [
          { required: true, message: '请上传右侧图', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[],
      scenesListData:[],

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;

      self.form.index_left_img  = editData.index_left_img;
      self.form.index_right_img = editData.index_right_img;
    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'product' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

     //接受富文本值
    editorVal(html) {
      this.form.index_product_details = html;
    },
     //接受富文本值
    editorValSpecifications(html) {
      this.form.index_performance = html;
    },

    //接受 值
    selectChange(val) {

      this.form.product_name = val;
    },

    //接受示例图值
    getUploadColorImg(val) {

      //console.log(val);
       this.form.index_left_img = val;
    },
    //接受示例图值
    getUploadImg(val) {

      //console.log(val);
       this.form.index_right_img = val;
    },


  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
