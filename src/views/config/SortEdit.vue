<template>
  <a-layout id="components-layout-demo-top-side-2" style="height:200%;">
    <topNav > </topNav>
    <a-layout>
      <leftSide selected="configSort"  open="sub2"></leftSide>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item > <a href="/#/configSort"><a-icon type="tool" /> 配置</a></a-breadcrumb-item>
          <a-breadcrumb-item > <a href="/#/configSort">分类</a></a-breadcrumb-item>
          <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
        </a-breadcrumb>

         <a-page-header
            style="border: 1px solid rgb(235, 237, 240);background:#fff;"
            title="分类"
            sub-title="编辑"
            @back="() => $router.go(-1)"
          />
          <br />

        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
        >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
            <a-form-model-item ref="name" label="分类名称" prop="config_sort">
              <a-input
                v-model="form.config_sort"
                placeholder="分类名称"
              />
            </a-form-model-item>
           
            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import atable   from "../../components/atable";
export default {
  components: { topNav, leftSide , atable},
  data() {
    return {
      //表单
      form: {
        config_sort: ''
      }, 
      //规则
       rules: {
        config_sort: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 2, max: 20, message: '最小长度 2 - 20 位', trigger: 'blur' },
        ]
      },

      id : 0
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.config_sort = editData.config_sort;
       

    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        if (!valid) {
          return false;
        }

        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

        let data = {
            config_sort : self.form.config_sort,
            table       : self.$utils.store.get('table')
        }
        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
