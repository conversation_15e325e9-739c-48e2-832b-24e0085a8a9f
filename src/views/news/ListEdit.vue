<template>
  <Layout selected="news" open="sub5">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/news/list"><a-icon type="book" /> 新闻</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="新闻"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
             <a-form-model-item label="新闻分类" prop="newsSortId">
              <a-select v-model="form.newsSortId" placeholder="请选择新闻分类">
                <a-select-option value="">
                请选择新闻分类
                </a-select-option>
                <a-select-option :value="item.id" v-for="item in sortListData" :key="item.id" @click="selectChange(item.news_sort)">
                {{item.news_sort}}
                </a-select-option>
              </a-select>
          
            </a-form-model-item>

            <a-form-model-item label="新闻标题" prop="newsTitle">
              <a-input
                v-model="form.newsTitle"
                placeholder="新闻标题"
              />
            </a-form-model-item>

            <a-form-model-item label="新闻简介" prop="newsSummary" >
              <a-textarea
                v-model="form.newsSummary"
                placeholder="新闻简介"
                :rows="4"
              />
            </a-form-model-item>

            
            <a-form-model-item label="封面" prop="newsCoverImg">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.newsCoverImg"></aupload>
            </a-form-model-item>
         

            <a-form-model-item label="内容" prop="newsContent">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.newsContent"></editor>
            </a-form-model-item>



            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
</template>
<script>


import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        newsSortId   : '',
        newsSortName : '',
        newsTitle    : '',
        newsCoverImg : '',
        newsSummary  : '',
        newsContent  : '',
        
      }, 
      //规则
       rules: {
        newsSortId: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        newsTitle: [
          { required: true, message: '请输入标题', trigger: 'blur' },
        ],
        newsSummary: [
          { required: true, message: '请输入简介', trigger: 'blur' },
        ],
        newsCoverImg: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
        newsContent: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[]

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.newsSortId   = editData.news_sort_id;
       self.form.newsSortName = editData.news_sort_name;
       self.form.newsTitle    = editData.news_title;
       self.form.newsSummary  = editData.news_summary;
       self.form.newsCoverImg = editData.news_cover_img;
       self.form.newsContent  = editData.news_content;
    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'newsSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

     //接受富文本值
    editorVal(html) {
      this.form.newsContent = html;
    },

    //接受 值
    selectChange(val) {

      this.form.newsSortName = val;
    },


    //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.newsCoverImg = val;
    },


  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
