  <template>
  <Layout selected="product" open="sub10">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="shopping-cart" /> 产品</a-breadcrumb-item>
      <a-breadcrumb-item>列表</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品"
      sub-title="列表"
    >
      <template slot="extra">
        <a-dropdown key="2">
          <a-menu slot="overlay" @click="onClick">
            <a-menu-item :key="0">全部</a-menu-item>
            <a-menu-item v-for="(item, index) in sortListData" :key="item.id">{{item.product_sort_name}}</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> {{buttonText}} <a-icon type="down" /> </a-button>
        </a-dropdown>

        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable
        :tabSourceData="tabSourceData"
        :tabColumns="tabColumns"
        :apiDel="apiDel"
        :buttonEditUrl='buttonEditUrl'
        :pagination="paginationConfig"
        @list="list"
        @pageChange="handlePageChange"
      ></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '分类',
            dataIndex: 'product_sort_name',
            key: 'product_sort_name'
          },
         {
            title: '场景',
            dataIndex: 'product_scenes_name',
            key: 'product_scenes_name'
          },
         {
            title: '名称',
            dataIndex: 'product_name',
            key: 'product_name'
          },
         {
            title: '品牌',
            dataIndex: 'product_brand',
            key: 'product_brand'
          },
           {
            title: '封面',
            dataIndex: 'product_img',
            key: 'product_img',
            scopedSlots: { customRender: 'imgAlert' }
          },
          {
            title: '详情图',
            dataIndex: 'product_img',
            key: 'product_img_info',
            scopedSlots: { customRender: 'infoImgUpload' }
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            scopedSlots: { customRender: 'create_time_label' }
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/api/common/dataDel",

      //按钮编辑前往地址
      buttonEditUrl:"/product/list/edit",
      sortListData : [],
      buttonText   : '筛选',

      // 分页配置
      paginationConfig: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
      },
      oldTabSourceData :[]
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'product');
  },
  mounted:function () {
    this.$utils.store.getS('token');

    this.sortList();
  },

  methods: {
     list(page = 1, pageSize = 10) {
        let self = this;
        self.$utils.http({
            data:{
              page  : page,
              pageSize: pageSize,
              table : 'product'
            },
            success:(res)=> {
              let data = res.data;

              // 如果API返回了分页信息，更新分页配置
              if (res.pagination) {
                self.paginationConfig = {
                  ...self.paginationConfig,
                  current: res.pagination.current || page,
                  total: res.pagination.total || 0,
                  pageSize: res.pagination.pageSize || pageSize
                };
              } else {
                // 如果没有分页信息，假设这是所有数据
                self.paginationConfig = {
                  ...self.paginationConfig,
                  current: page,
                  total: data.length,
                  pageSize: pageSize
                };
              }

              data.forEach(element => {
                element.key = element.id,
                element.create_time = self.$utils.getDate(element.create_time);
              });

              self.tabSourceData = data;

            }

          }, '/api/common/pageList')

    },

    // 处理分页变化
    handlePageChange(paginationInfo) {
      this.list(paginationInfo.current, paginationInfo.pageSize);
    },
    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'productSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

    onClick({ key  }) {
      let self          = this;
      let tabSourceData = self.tabSourceData;
      let newSourceData = [];
      let sortListData  = self.sortListData;
      if(key == 0) {
        self.buttonText = '筛选'
        self.list();
        return ;
      }

      if(self.oldTabSourceData.length == 0) {
        self.oldTabSourceData = tabSourceData;
      }


      let oldTabSourceData = self.oldTabSourceData;

      oldTabSourceData.forEach(element => {
          element.key         = element.id,
          element.create_time = self.$utils.getDate(element.create_time);
          if(key  == Number(element.product_sort_id)) {
            newSourceData.push(element);
          }
      });
      
      self.tabSourceData = newSourceData;

      sortListData.forEach(element => {
          if(key  == Number(element.id)) {
            self.buttonText = element.product_sort_name;
          }
      });

    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
