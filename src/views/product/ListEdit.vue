<template>
  <Layout selected="product" open="sub2">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/product/list"><a-icon type="shopping" /> 产品</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
             <a-form-model-item label="分类" prop="product_sort_id">
              <a-select v-model="form.product_sort_id" placeholder="产品分类">
                <a-select-option value="">
                请选择产品分类
                </a-select-option>
                <a-select-option :value="item.id" v-for="item in sortListData" :key="item.id" @click="selectChange(item.product_sort_name)">
                {{item.product_sort_name}}
                </a-select-option>
              </a-select>
          
            </a-form-model-item>


             <a-form-model-item label="场景" prop="product_scenes_id">
              <a-select v-model="form.product_scenes_id" placeholder="产品分类" mode="multiple" @change="productScenesIdChange">
                <a-select-option value="">
                请选择产品场景
                </a-select-option>
                <a-select-option :value="item.id" v-for="item in scenesListData" :key="item.id">
                {{item.product_scenes_name}}
                </a-select-option>
              </a-select>
          
            </a-form-model-item>

            <a-form-model-item label="名称" prop="product_name">
              <a-input
                v-model="form.product_name"
                placeholder="产品名称"
              />
            </a-form-model-item>
            <a-form-model-item label="品牌" prop="product_brand">
              <a-input
                v-model="form.product_brand"
                placeholder="产品品牌"
              />
            </a-form-model-item>
            <a-form-model-item label="型号" prop="product_model">
              <a-input
                v-model="form.product_model"
                placeholder="产品型号"
              />
            </a-form-model-item>
            <a-form-model-item label="规格" prop="product_standard">
              <a-input
                v-model="form.product_standard"
                placeholder="产品规格"
              />
            </a-form-model-item>

            <a-form-model-item label="类型" prop="product_type">
              <a-input
                v-model="form.product_type"
                placeholder="产品类型"
              />
            </a-form-model-item>

            <a-form-model-item label="颜色图片" prop="product_color_img">
             <auploadMutil func="getUploadColorImg" @getUploadColorImg="getUploadColorImg($event)" :sourceValue="form.product_color_img" :uploadLength="15"></auploadMutil>
            </a-form-model-item>


            <a-form-model-item label="封面" prop="product_img">
             <aupload func="getUploadImg" @getUploadImg="getUploadImg($event)" :sourceValue="form.product_img"></aupload>
            </a-form-model-item>
         

            <a-form-model-item label="详情" prop="product_details">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.product_details"></editor>
            </a-form-model-item>


            <a-form-model-item label="规格参数" prop="product_specifications">
              <editor func="editorValSpecifications" @editorValSpecifications="editorValSpecifications($event)" :sourceValue="form.product_specifications"></editor>
            </a-form-model-item>


            <a-form-model-item label="优势特点" prop="product_advantages">
              <editor func="editorValAdvantages" @editorValAdvantages="editorValAdvantages($event)" :sourceValue="form.product_advantages"></editor>
            </a-form-model-item>


            <a-form-model-item label="售后支持" prop="product_support">
              <editor func="editorValSupport" @editorValSupport="editorValSupport($event)" :sourceValue="form.product_support"></editor>
            </a-form-model-item>



            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";
import auploadMutil from "../../components/auploadMutil";



export default {
  components: { topNav, leftSide , editor, aupload, auploadMutil},
  data() {
    return {
      //表单
      form: {
        product_name   : '',
        product_brand : '',
        product_type    : '',
        product_model : '',
        product_standard  : '',
        product_color_img  : [],
        product_img  : '',
        product_details  : '',
        product_specifications  : '',
        product_advantages  : '',
        product_support  : '',
        product_sort_id  : '',
        product_sort_name  : '',
        product_scenes_name  : [],
        product_scenes_id  : [],
        product_type  : '',
        
      }, 
      //规则
       rules: {
        product_sort_id: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        product_scenes_id: [
          { required: true, message: '请选择场景', trigger: 'blur' }
        ],
        product_name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        product_color_img: [
          { required: true, message: '请上传颜色图片', trigger: 'blur' },
        ],
        product_img: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
        product_details: [
          { required: true, message: '请输入详情', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[],
      scenesListData:[],

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;
       self.form.product_sort_name = editData.product_sort_name;

      if(editData.product_color_img == '') {
        self.form.product_color_img = [];
      } else {
        self.form.product_color_img = JSON.parse(editData.product_color_img);
      }

     
       self.form.product_scenes_id = JSON.parse(editData.product_scenes_id);
       
       

    }

    self.sortList();
    self.scenesList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }

        let scenesString = '';
        data.product_scenes_id.forEach(val => {
            self.scenesListData.forEach( e => {
              if(e.id == val) {
                scenesString += e.product_scenes_name+',';
              }
            });
        });


        data.product_color_img   = JSON.stringify(data.product_color_img);
        data.product_scenes_id   = JSON.stringify(data.product_scenes_id);
        data.product_scenes_name = scenesString.substring(0, scenesString.length-1);
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'productSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },
    scenesList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'productScenes' 
            },
            success:(res)=> {
              let data = res.data;
              self.scenesListData = data;

            }
          }, '/api/common/pageList')
      
    },

     //接受富文本值
    editorVal(html) {
      this.form.product_details = html;
    },
     //接受富文本值
    editorValSpecifications(html) {
      this.form.product_specifications = html;
    },
     //接受富文本值
    editorValAdvantages(html) {
      this.form.product_advantages = html;
    },
     //接受富文本值
    editorValSupport(html) {
      this.form.product_support = html;
    },

    //接受 值
    selectChange(val) {

      this.form.product_sort_name = val;
    },


    //接受示例图值
    getUploadColorImg(val) {

      //console.log(val);
       this.form.product_color_img.push(val);
    },
    //接受示例图值
    getUploadImg(val) {

      //console.log(val);
       this.form.product_img = val;
    },

    productScenesIdChange(value, option) {
      this.product_scenes_id = value;
     
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
