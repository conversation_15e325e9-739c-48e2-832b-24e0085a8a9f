<template>
  <Layout selected="productScenes" open="sub10">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a-icon type="shopping-cart" /> 产品</a-breadcrumb-item>
      <a-breadcrumb-item>场景</a-breadcrumb-item>
    </a-breadcrumb>
    
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="场景"
      sub-title="列表"
    >
      <template slot="extra">
        <a-button key="1" icon="plus" type="primary" @click="$router.push({path:buttonEditUrl})">
          添加
        </a-button>
      </template>
    </a-page-header>
    
    <br />
    
    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0 }"
    >
      <atable :tabSourceData="tabSourceData" :tabColumns="tabColumns" :apiDel="apiDel" :buttonEditUrl='buttonEditUrl'></atable>
    </a-layout-content>
  </Layout>
</template>

<script>
import Layout from "../Layout";
import atable from "../../components/atable";
export default {
  components: { Layout, atable},
  data() {
    return {

      //表格数据
      tabSourceData : [],
      //表格标题
      tabColumns    : [
         {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
          },
         {
            title: '中文名称',
            dataIndex: 'product_scenes_name',
            key: 'product_scenes_name'
          },
         {
            title: '英文名称',
            dataIndex: 'product_scenes_english_name',
            key: 'product_scenes_english_name'
          },
         {
            title: '副标题',
            dataIndex: 'product_scenes_subtitle',
            key: 'product_scenes_subtitle'
          },
         {
            title: '图标',
            dataIndex: 'product_scenes_icon',
            key: 'product_scenes_icon',
             scopedSlots: { customRender: 'imgAlert' }
          },
         {
            title: '描述',
            dataIndex: 'product_scenes_info',
            key: 'product_scenes_info',
             scopedSlots: { customRender: 'toolTip' }
          },
         {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            scopedSlots: { customRender: 'create_time_label' }
          },
          {
            title: '编辑',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }
          }
      ],
      //api 删除地址
      apiDel:"/api/common/dataDel",

      //按钮编辑前往地址
      buttonEditUrl:"/product/scenes/edit"
    };
  },

  created: function () {
    this.list();

    this.$utils.store.set('table', 'productScenes');
  },
  mounted:function () {
    this.$utils.store.getS('token');
  },

  methods: {
    list() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'productScenes' 
            },
            success:(res)=> {
              let data = res.data;
              
              data.forEach(element => {
                element.key = element.id,
                element.create_time = self.$utils.getDate(element.create_time);
              });
              
              self.tabSourceData = data;

            }

          }, '/api/common/pageList')
      
    }
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
