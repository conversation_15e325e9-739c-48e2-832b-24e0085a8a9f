<template>
  <a-layout id="components-layout-demo-top-side-2" style="height:200%;">
    <topNav > </topNav>
    <a-layout>
      <leftSide selected="productScenes"  open="sub10"></leftSide>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item><a-icon type="shopping-cart" /> 产品</a-breadcrumb-item>
          <a-breadcrumb-item>场景</a-breadcrumb-item>
          <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
        </a-breadcrumb>

         <a-page-header
            style="border: 1px solid rgb(235, 237, 240);background:#fff;"
            title="场景"
            sub-title="编辑"
            @back="() => $router.go(-1)"
          />
          <br />

        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
        >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
            <a-form-model-item label="中文名称" prop="product_scenes_name">
              <a-input
                v-model="form.product_scenes_name"
                placeholder="中文名称"
              />
            </a-form-model-item>
            <a-form-model-item  label="英文名称" prop="product_scenes_english_name">
              <a-input
                v-model="form.product_scenes_english_name"
                placeholder="英文名称"
              />
            </a-form-model-item>

            <a-form-model-item  label="副标题" prop="product_scenes_subtitle">
              <a-input
                v-model="form.product_scenes_subtitle"
                placeholder="副标题"
              />
            </a-form-model-item>

            <a-form-model-item label="图标" prop="product_scenes_icon">
             <aupload func="getUpload" @getUpload="getUpload($event)" :sourceValue="form.product_scenes_icon"></aupload>
            </a-form-model-item>

            <a-form-model-item label="描述" prop="product_scenes_info" >
              <a-textarea
                v-model="form.product_scenes_info"
                placeholder="描述"
                :rows="4"
              />
            </a-form-model-item>
           
            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script>
import topNav   from "../../components/topNav";
import leftSide from "../../components/leftSide";
import atable   from "../../components/atable";
import aupload   from "../../components/aupload";
export default {
  components: { topNav, leftSide , atable, aupload},
  data() {
    return {
      //表单
      form: {
        product_scenes_name         : '',
        product_scenes_english_name : '',
        product_scenes_subtitle     : '',
        product_scenes_icon         : '',
        product_scenes_info         : '',
      }, 
      //规则
       rules: {
        product_scenes_name: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
        ],
        product_scenes_english_name: [
          { required: true, message: '请输入英文名称', trigger: 'blur' },
        ],
        product_scenes_subtitle: [
          { required: true, message: '请输入副标题', trigger: 'blur' },
        ],
        product_scenes_icon: [
          { required: true, message: '请上传图标', trigger: 'blur' },
        ],
        product_scenes_info: [
          { required: true, message: '请输入描述', trigger: 'blur' },
        ],
      },

      id : 0
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;
       self.form.product_scenes_icon = editData.product_scenes_icon;
       

    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        if (!valid) {
          return false;
        }

        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

        
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/common/dataEdit')
      
    },

     //接受示例图值
    getUpload(val) {

      //console.log(val);
       this.form.product_scenes_icon = val;
    },
  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
