<template>
  <Layout selected="qa" open="sub6">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/qa/list"><a-icon type="question-circle" /> 常见问题</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="常见问题"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
           
          
            </a-form-model-item>

            <a-form-model-item label="问题" prop="question">
              <a-input
                v-model="form.question"
                placeholder="问题"
              />
            </a-form-model-item>

            <a-form-model-item label="解答" prop="answer" >
              <a-textarea
                v-model="form.answer"
                placeholder="解答"
                :rows="4"
              />
            </a-form-model-item>

            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
  </Layout>
</template>
<script>


import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        question : '',
        answer   : '',
      }, 
      //规则
       rules: {
        question: [
          { required: true, message: '请输入问题', trigger: 'blur' }
        ],
        answer: [
          { required: true, message: '请输入解答', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[]

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.question = editData.question;
       self.form.answer   = editData.answer;
    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/qa/edit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'newsSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
