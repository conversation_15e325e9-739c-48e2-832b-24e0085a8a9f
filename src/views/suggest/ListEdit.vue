<template>
  <Layout selected="suggest" open="sub8">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/suggest/list"><a-icon type="message" /> 投诉建议</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="投诉建议"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
           
          
            </a-form-model-item>

            <a-form-model-item label="标题" prop="title">
              <a-input
                v-model="form.title"
                placeholder="标题"
              />
            </a-form-model-item>

            <a-form-model-item label="内容" prop="content" >
              <a-textarea
                v-model="form.content"
                placeholder="内容"
                :rows="4"
              />
            </a-form-model-item>

            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
      </Layout>
</template>
<script>


import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";



export default {
  components: { Layout, editor, aupload},
  data() {
    return {
      //表单
      form: {
        title : '',
        content   : '',
      }, 
      //规则
       rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ]
      },
      id : 0,

      sortListData:[]

    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form.title   = editData.title;
       self.form.content = editData.content;
    }

    self.sortList();

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;
        data.table = self.$utils.store.get('table')

        if(self.id != 0) {
           data.id = id;
        }
        self.$utils.http({
            data : data,
            success:(res)=> {
              let data = res.data;
              
              self.$message.success(res.message);

               self.$router.go('-1');

            }

          }, '/api/service/edit')
      
    },

    sortList() {
        let self = this;
        self.$utils.http({
            data:{
              page  : 1,
              table : 'newsSort' 
            },
            success:(res)=> {
              let data = res.data;
              self.sortListData = data;

            }
          }, '/api/common/pageList')
      
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
