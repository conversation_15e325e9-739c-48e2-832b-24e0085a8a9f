const webpack = require("webpack")


module.exports = {

  // 配置生成dist里面static的cdn资源路径（测试环境为./，正式环境走cdn路径）
  //publicPath: 'http://demo.502bg.com/xinsiaodun/admin/',
  publicPath: './',


  lintOnSave: false, // 禁止eslint
  devServer: {
      open: false, // 构建完成自动打开浏览器
      port: 8080, // 开发服务器端口
      proxy: {
        // 代理所有以/api开头的请求
        '/admin': {
          target: 'http://**************:8081', // 目标服务器地址
          changeOrigin: true, // 改变请求头中的host
          ws: true, // 支持websocket
        //   pathRewrite: {
        //     '^/admin': '' // 重写路径，去掉/admin前缀
        //   },
          // 日志输出
          logLevel: 'debug'
        }
      }
  },

  configureWebpack: {
      plugins: [
          // 全局配置node_modules中的模块，使用时无需引入
          new webpack.ProvidePlugin({
              $: "jquery",
              jQuery: "jquery",
              "windows.jQuery": "jquery"
          })
      ]

  },

  // webpack 链接 API，用于生成和修改 webapck 配置
  chainWebpack: (config) => {
      // 取消 chunks，每个页面只对应一个单独的 JS / CSS
      config.optimization.splitChunks({
          cacheGroups: {}
      });

      // config
      //     .plugin('webpack-bundle-analyzer')
      //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
  },

  pluginOptions: {

  }

}
